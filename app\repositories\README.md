# Repository 层架构文档

## 概述

Repository层是向量数据库访问的抽象层，提供了一套完整的数据访问接口，将业务逻辑与具体的数据库实现分离。

## 架构设计

### 分层结构
```
routes.py (API层)
    ↓
services/ (业务逻辑层) [待实现]  
    ↓
repositories/ (数据访问层) [当前层]
    ↓
core/vectordb.py (数据库核心层)
```

### 设计原则

1. **单一职责**：每个Repository专注特定的数据操作
2. **抽象封装**：隐藏底层数据库API的复杂性
3. **错误处理**：统一的异常处理和错误信息
4. **类型安全**：完整的Python类型提示
5. **反射机制**：安全地调用可能不存在的数据库方法

## 组件详解

### 1. VectorDatabaseRepository
**职责**：数据库连接和管理

**核心功能**：
- 数据库连接创建和缓存
- 自动数据库创建
- 连接生命周期管理

**使用示例**：
```python
from app.repositories import VectorDatabaseRepository
from app.config.settings import VectorDBConfig

# 初始化
db_repo = VectorDatabaseRepository()
db_repo.set_config(VectorDBConfig(
    db_type="milvus",
    uri="http://localhost:19530",
    token=""
))

# 获取连接（自动创建数据库）
db = await db_repo.get_database_connection(
    database="my_project", 
    create_if_not_exists=True
)

# 手动创建数据库
await db_repo.create_database("new_project")
```

### 2. CollectionRepository
**职责**：集合管理操作

**核心功能**：
- 普通向量集合创建
- 混合检索集合创建
- 集合信息查询和统计
- 集合生命周期管理

**使用示例**：
```python
from app.repositories import CollectionRepository

collection_repo = CollectionRepository(db_connection)

# 创建普通集合
await collection_repo.create_collection(
    collection_name="documents",
    vector_dimension=768,
    auto_id=True
)

# 创建混合检索集合
await collection_repo.create_hybrid_collection(
    collection_name="hybrid_docs",
    dense_vector_dim=768
)

# 集合管理
exists = await collection_repo.collection_exists("documents")
info = await collection_repo.get_collection_info("documents")
collections = await collection_repo.list_collections()
```

### 3. VectorRepository
**职责**：向量数据的CRUD操作

**核心功能**：
- 向量插入（单条、批量、并发）
- 混合检索向量插入
- Upsert操作
- 向量删除
- 向量记录构建

**使用示例**：
```python
from app.repositories import VectorRepository

vector_repo = VectorRepository(db_connection)

# 构建向量记录
record = vector_repo.build_normal_vector_record(
    content="文档内容",
    vector=[0.1] * 768,
    metadata={"category": "tech"}
)

# 插入向量
await vector_repo.insert_vectors("documents", [record])

# 批量插入
await vector_repo.batch_insert_vectors(
    collection_name="documents",
    records=records,
    batch_size=20
)

# 并发插入（大数据量）
await vector_repo.concurrent_insert_vectors(
    collection_name="documents",
    records=large_records,
    batch_size=50,
    max_concurrent=3
)
```

### 4. SearchRepository
**职责**：向量搜索操作

**核心功能**：
- 基本向量搜索
- 多集合搜索
- 分页搜索
- 重排序搜索
- 带筛选条件搜索

**使用示例**：
```python
from app.repositories import SearchRepository

search_repo = SearchRepository(db_connection)

# 基本搜索
results = await search_repo.search_vectors(
    collection_name="documents",
    query_vector=query_vector,
    top_k=5
)

# 带筛选条件搜索
filtered_results = await search_repo.search_with_filters(
    collection_name="documents",
    query_vector=query_vector,
    top_k=5,
    metadata_filters={"category": "tech"}
)

# 分页搜索
page_result = await search_repo.search_with_pagination(
    collection_name="documents",
    query_vector=query_vector,
    page=1,
    page_size=10
)
```

### 5. HybridRepository
**职责**：混合检索操作

**核心功能**：
- 混合检索（语义+全文）
- 单一检索策略
- 重排序策略配置

**使用示例**：
```python
from app.repositories import HybridRepository

hybrid_repo = HybridRepository(db_connection)

# 混合检索
results = await hybrid_repo.search_hybrid_vectors(
    collection_name="hybrid_docs",
    query_text="查询文本",
    dense_vector=query_vector,
    top_k=5,
    search_strategy="hybrid",  # 'semantic', 'full_text', 'hybrid'
    rerank_strategy="rrf"      # 'rrf', 'weighted'
)
```

## 反射机制说明

### 为什么使用反射？

Repository层大量使用了Python的反射机制（`hasattr` + `getattr`），原因如下：

1. **类型兼容性**：`VectorDatabase`是抽象基类，不同实现可能有不同方法
2. **安全性**：避免调用不存在的方法导致`AttributeError`
3. **扩展性**：支持未来新增的数据库类型和功能

### 反射模式示例

```python
# 标准反射模式
if hasattr(self.db, 'method_name'):
    method = getattr(self.db, 'method_name')
    result = await method(*args, **kwargs)
else:
    raise Exception("数据库不支持此操作")
```

### 反射 vs 直接调用

```python
# ❌ 直接调用（可能失败）
await self.db.create_database(name)

# ✅ 反射调用（安全）
if hasattr(self.db, 'create_database'):
    await self.db.create_database(name)
else:
    raise Exception("不支持数据库创建")
```

## 错误处理策略

### 1. 统一异常格式
```python
try:
    # 数据库操作
    result = await self.db.some_operation()
except Exception as e:
    print(f"[错误] 操作失败: {e}")
    raise Exception(f"操作失败: {str(e)}")
```

### 2. 优雅降级
```python
try:
    # 尝试新方法
    return await self.db.new_method()
except:
    # 回退到兼容方法
    return await self.db.legacy_method()
```

### 3. 警告而非失败
```python
try:
    await self.db.optional_operation()
except Exception as e:
    print(f"[警告] 可选操作失败: {e}")
    # 不抛出异常，继续执行
```

## 性能优化

### 1. 连接复用
```python
# VectorDatabaseRepository 自动缓存连接
connection_key = database or "default"
if connection_key in self._connections:
    return self._connections[connection_key]
```

### 2. 批量操作
```python
# 大数据量使用批量插入
await vector_repo.batch_insert_vectors(
    collection_name="docs",
    records=large_dataset,
    batch_size=100  # 根据内存调整
)
```

### 3. 并发处理
```python
# 超大数据量使用并发插入
await vector_repo.concurrent_insert_vectors(
    collection_name="docs",
    records=huge_dataset,
    batch_size=50,
    max_concurrent=5  # 根据数据库负载调整
)
```

## 最佳实践

### 1. 资源管理
```python
# ✅ 正确的资源管理
db_repo = VectorDatabaseRepository()
try:
    db_repo.set_config(config)
    db = await db_repo.get_database_connection()
    # 执行操作
finally:
    db_repo.close_all_connections()
```

### 2. 错误处理
```python
# ✅ 完善的错误处理
try:
    result = await vector_repo.insert_vectors(collection, records)
    print(f"成功插入 {result} 条记录")
except Exception as e:
    print(f"插入失败: {e}")
    # 记录错误日志
    # 可能的重试逻辑
    raise
```

### 3. 参数验证
```python
# ✅ 参数验证
if not collection_name:
    raise ValueError("集合名称不能为空")
if not records:
    raise ValueError("记录列表不能为空")
if vector_dimension <= 0:
    raise ValueError("向量维度必须大于0")
```

### 4. 日志记录
```python
# ✅ 详细的操作日志
print(f"[向量] 开始插入向量到集合 '{collection_name}'")
print(f"[向量] 记录数: {len(records)}, 批次大小: {batch_size}")
# ... 执行操作
print(f"[成功] 向量插入完成，耗时: {elapsed_time:.3f}秒")
```

## 扩展指南

### 添加新的Repository

1. **创建Repository类**：
```python
class NewRepository:
    def __init__(self, db: VectorDatabase):
        self.db = db
```

2. **实现核心方法**：
```python
async def new_operation(self, *args, **kwargs):
    if hasattr(self.db, 'new_operation'):
        method = getattr(self.db, 'new_operation')
        return await method(*args, **kwargs)
    else:
        raise Exception("数据库不支持此操作")
```

3. **添加到__init__.py**：
```python
from .new_repository import NewRepository

__all__ = [
    # ... existing repositories
    "NewRepository"
]
```

### 添加新方法到现有Repository

1. 遵循现有的命名约定
2. 使用完整的类型提示
3. 实现错误处理和日志记录
4. 添加详细的文档字符串

## 测试建议

### 1. 单元测试
```python
import pytest
from app.repositories import VectorRepository

@pytest.mark.asyncio
async def test_insert_vectors():
    # 模拟数据库连接
    mock_db = MockVectorDatabase()
    vector_repo = VectorRepository(mock_db)
    
    # 测试插入
    records = [{"content": "test", "vector": [0.1] * 768}]
    result = await vector_repo.insert_vectors("test_collection", records)
    
    assert result == 1
```

### 2. 集成测试
```python
@pytest.mark.asyncio
async def test_full_workflow():
    # 使用真实数据库连接
    db_repo = VectorDatabaseRepository()
    # ... 完整的工作流测试
```

## 监控和调试

### 1. 性能监控
```python
# 使用内置的统计功能
stats = search_repo.get_search_statistics()
insert_stats = vector_repo.get_insert_statistics()
```

### 2. 调试日志
Repository层内置了详细的日志输出，可以通过日志级别控制：
- `[信息]`：正常操作信息
- `[警告]`：非致命错误
- `[错误]`：操作失败
- `[成功]`：操作完成

这个Repository层的实现为整个向量数据库系统提供了稳定、高效、易扩展的数据访问基础设施。 