import requests
import json
import sys
import time
import argparse

# 测试多数据库搜索API的本地测试脚本
def test_multi_search_local():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='测试多数据库搜索API')
    parser.add_argument('--host', default='http://localhost:8000', help='API主机地址')
    parser.add_argument('--query', default='这是一个测试查询', help='搜索查询文本')
    parser.add_argument('--db1', default='default', help='第一个数据库名称')
    parser.add_argument('--db2', default=None, help='第二个数据库名称（可选）')
    parser.add_argument('--collection', default='documents', help='要搜索的集合名称')
    args = parser.parse_args()
    
    # 设置API地址
    base_url = args.host
    url = f"{base_url}/api/v1/multi_search"
    
    # 准备数据库配置
    databases = [args.db1]
    if args.db2:
        databases.append(args.db2)
    
    print(f"使用主机: {base_url}")
    print(f"搜索查询: {args.query}")
    print(f"数据库: {databases}")
    print(f"集合: {args.collection}")
    
    # 测试用例1: 使用字符串指定数据库（搜索所有集合）
    payload1 = {
        "text": args.query,
        "top_k": 5,
        "total_results": 10,
        "databases": databases
    }
    
    # 测试用例2: 使用对象指定数据库和集合
    payload2 = {
        "text": args.query,
        "top_k": 5,
        "total_results": 10,
        "databases": [
            {
                "database": args.db1,
                "collections": [args.collection]
            }
        ]
    }
    
    # 测试用例3: 混合使用字符串和对象
    payload3 = {
        "text": args.query,
        "top_k": 5,
        "total_results": 10,
        "databases": [
            args.db1
        ]
    }
    if args.db2:
        payload3["databases"].append({
            "database": args.db2,
            "collections": [args.collection]
        })
    
    # 测试用例4: 错误处理 - 空数据库列表
    payload4 = {
        "text": args.query,
        "top_k": 5,
        "total_results": 10,
        "databases": []
    }
    
    # 测试用例5: 错误处理 - 不存在的数据库
    payload5 = {
        "text": args.query,
        "top_k": 5,
        "total_results": 10,
        "databases": ["non_existent_db"]
    }
    
    # 测试所有用例
    test_cases = [
        ("使用字符串指定数据库", payload1),
        ("使用对象指定数据库和集合", payload2),
        ("混合使用字符串和对象", payload3),
        ("错误处理 - 空数据库列表", payload4),
        ("错误处理 - 不存在的数据库", payload5)
    ]
    
    # 如果没有第二个数据库，跳过测试用例3
    if not args.db2:
        test_cases = [tc for tc in test_cases if tc[0] != "混合使用字符串和对象"]
    
    for name, payload in test_cases:
        print(f"\n\n{'='*50}")
        print(f"测试用例: {name}")
        print(f"{'='*50}")
        print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            headers = {"Content-Type": "application/json"}
            
            # 转换为JSON字符串
            payload_str = json.dumps(payload)
                
            print(f"发送请求到: {url}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 发送请求
            response = requests.post(url, data=payload_str, headers=headers)
            
            # 计算耗时
            elapsed_time = time.time() - start_time
            
            print(f"状态码: {response.status_code}")
            print(f"请求耗时: {elapsed_time:.3f}秒")
            
            if response.status_code == 200:
                # 解析JSON响应
                try:
                    result = response.json()
                    print(f"查询文本: {result.get('query')}")
                    print(f"搜索耗时: {result.get('search_time')}")
                    print(f"搜索的数据库数量: {result.get('databases_searched')}")
                    print(f"总结果数: {result.get('total_results')}")
                    
                    # 打印结果
                    results = result.get('results', [])
                    print(f"\n找到 {len(results)} 条结果:")
                    
                    # 按数据库和集合分组显示结果
                    db_collection_results = {}
                    for r in results:
                        db = r.get('database', '未知数据库')
                        coll = r.get('collection', '未知集合')
                        key = f"{db}/{coll}"
                        if key not in db_collection_results:
                            db_collection_results[key] = []
                        db_collection_results[key].append(r)
                    
                    # 显示分组结果
                    for key, group_results in db_collection_results.items():
                        print(f"\n  来自 {key} 的结果 ({len(group_results)}条):")
                        for i, r in enumerate(group_results[:2]):
                            print(f"    结果 {i+1}:")
                            print(f"      内容: {r.get('content', '')[:100]}...")
                            print(f"      相似度: {r.get('similarity', 0):.4f}")
                        if len(group_results) > 2:
                            print(f"      ... 还有 {len(group_results) - 2} 条结果")
                    
                    print("\n✅ 测试成功!")
                except json.JSONDecodeError:
                    print("❌ 响应不是有效的JSON格式!")
                    print(f"响应体: {response.text}")
            else:
                print("❌ 测试失败!")
                print(f"响应体: {response.text}")
        except Exception as e:
            print(f"❌ 测试出错: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_multi_search_local()
