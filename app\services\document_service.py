"""
文档处理服务

负责：
- 文档上传和解析
- 文本分块和预处理
- 向量化处理
- 批量文档处理
- 混合检索处理
- 加密功能集成
"""

import time
import asyncio
from typing import Dict, List, Any, Optional, Union
from fastapi import UploadFile

from app.config.settings import VectorDBConfig
from app.repositories import VectorRepository, VectorDatabaseRepository, CollectionRepository
from app.services.embedding_service import EmbeddingService
from app.utils.logging_utils import OperationLogger, TimeTracker, ProgressLogger
from app.utils.validation import ParameterValidator
from app.utils.response_utils import ResponseBuilder, UploadResponseBuilder
from app.utils.data_utils import RecordBuilder, ListProcessor, TextProcessor, IDGenerator
from app.core.knowledge_processing import parse_pdf, parse_docx, parse_txt
from app.core.encryption import encrypt_text


class DocumentService:
    """文档处理服务"""
    
    def __init__(self):
        """初始化文档处理服务"""
        self.db_repo = VectorDatabaseRepository()
        self.vector_repo: Optional[VectorRepository] = None
        self.collection_repo: Optional[CollectionRepository] = None
        self.embedding_service: Optional[EmbeddingService] = None
        self.operation_logger = OperationLogger()
        self.validator = ParameterValidator()
        self.response_builder = ResponseBuilder()
        self.upload_response_builder = UploadResponseBuilder()
        self.record_builder = RecordBuilder()
        self.list_processor = ListProcessor()
        self.text_processor = TextProcessor()
        self.progress_logger = ProgressLogger()
        self._initialized = False
    
    async def initialize(self, config: VectorDBConfig) -> Dict[str, Any]:
        """
        初始化文档处理服务
        
        Args:
            config: 数据库配置
            
        Returns:
            Dict[str, Any]: 初始化结果
        """
        start_time = time.time()
        
        try:
            # 设置数据库配置
            self.db_repo.set_config(config)
            
            # 获取数据库连接
            db_connection = await self.db_repo.get_database_connection()
            
            # 初始化repositories
            self.vector_repo = VectorRepository(db_connection)
            self.collection_repo = CollectionRepository(db_connection)
            
            # 初始化向量化服务
            self.embedding_service = EmbeddingService()
            
            self._initialized = True
            elapsed_time = time.time() - start_time
            
            self.operation_logger.log_operation_success(
                operation_name="文档处理服务初始化",
                initialization_time=f"{elapsed_time:.3f}秒"
            )
            
            return self.response_builder.success_response(
                message="文档处理服务初始化成功",
                data={
                    "initialization_time": elapsed_time,
                    "status": "ready"
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="文档处理服务初始化",
                error=e
            )
            raise Exception(f"文档处理服务初始化失败: {str(e)}")
    
    def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            raise Exception("文档处理服务未初始化，请先调用initialize方法")
        if not self.vector_repo:
            raise Exception("向量仓库未初始化")
        if not self.embedding_service:
            raise Exception("向量化服务未初始化")

    async def upload_document(
        self,
        file: UploadFile,
        collection_name: str,
        chunk_size: int = 500,
        chunk_overlap: int = 50,
        encrypt: bool = False,
        database: Optional[str] = None,
        embedding_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        上传并处理文档文件
        
        Args:
            file: 上传的文档文件
            collection_name: 集合名称
            chunk_size: 分块大小
            chunk_overlap: 分块重叠大小
            encrypt: 是否加密
            database: 数据库名称
            embedding_type: 向量模型类型
            
        Returns:
            Dict[str, Any]: 文档上传结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("文档上传处理")
        timer.start()
        
        try:
            # 参数验证
            self.validator.validate_chunk_parameters(chunk_size, chunk_overlap)
            if not file.filename:
                raise ValueError("文件名不能为空")
            file_type = self.validator.validate_file_type(file.filename)
            
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
                self.collection_repo = CollectionRepository(db_connection)
            
            # 读取文件内容
            content = await file.read()
            
            # 文件头验证和解析
            if file_type == 'pdf':
                if not content.startswith(b'%PDF-'):
                    raise ValueError("Invalid PDF header")
                text = parse_pdf(content)
            elif file_type == 'docx':
                if not content.startswith(b'PK\x03\x04'):
                    raise ValueError("Invalid DOCX header")
                text = parse_docx(content)
            elif file_type == 'txt':
                text = parse_txt(content)
            else:
                raise ValueError(f"不支持的文档类型: {file_type}")
            
            if not text.strip():
                raise ValueError("文档内容解析为空")
            
            parse_time = timer.checkpoint("文档解析")
            
            # 生成文档ID
            doc_id = IDGenerator.generate_doc_id()
            
            # 文档分块
            chunks = self.text_processor.chunk_text(text, chunk_size, chunk_overlap)
            chunk_time = timer.checkpoint("文档分块")
            
            self.operation_logger.log_operation_success(
                operation_name="文档预处理",
                filename=file.filename,
                file_type=file_type,
                doc_id=doc_id,
                chunks_count=len(chunks),
                parse_time=f"{parse_time:.3f}秒",
                chunk_time=f"{chunk_time:.3f}秒"
            )
            
            # 创建集合
            await self._ensure_collection_exists(collection_name, embedding_type=embedding_type)
            
            # 生成向量并存储
            result = await self._process_chunks_to_vectors(
                chunks=chunks,
                doc_id=doc_id,
                collection_name=collection_name,
                encrypt=encrypt,
                embedding_type=embedding_type,
                metadata={
                    "filename": file.filename,
                    "file_type": file_type,
                    "chunk_size": chunk_size,
                    "chunk_overlap": chunk_overlap
                }
            )
            
            elapsed_time = timer.finish()
            
            # 构建额外信息
            extra_info = {
                "filename": file.filename,
                "file_type": file_type,
                "encrypted": encrypt,
                "time_breakdown": {
                    "parse_time": f"{parse_time:.3f}秒",
                    "chunk_time": f"{chunk_time:.3f}秒",
                    "vector_time": f"{result.get('vector_time', 0):.3f}秒"
                }
            }
            
            # 构建响应
            response = self.upload_response_builder.document_upload_response(
                doc_id=doc_id,
                chunk_count=len(chunks),
                vector_dimension=result.get('vector_dimension', 768),
                processing_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            # 添加额外信息到响应数据
            if 'data' in response:
                response['data'].update(extra_info)
            
            return response
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="文档上传处理",
                error=e,
                filename=file.filename if file and file.filename else "unknown"
            )
            raise Exception(f"文档上传处理失败: {str(e)}")

    async def upload_texts(
        self,
        texts: List[str],
        collection_name: str,
        metadata: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[Union[str, int]]] = None,
        encrypt: bool = False,
        auto_flush: bool = True,
        database: Optional[str] = None,
        embedding_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        上传文本列表
        
        Args:
            texts: 文本列表
            collection_name: 集合名称
            metadata: 元数据列表
            ids: ID列表
            encrypt: 是否加密
            auto_flush: 是否自动刷新
            database: 数据库名称
            embedding_type: 向量模型类型
            
        Returns:
            Dict[str, Any]: 文本上传结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("文本上传处理")
        timer.start()
        
        try:
            # 参数验证
            if not texts:
                raise ValueError("文本列表不能为空")
            
            self.validator.validate_ids_consistency(ids, texts)
            
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
                self.collection_repo = CollectionRepository(db_connection)
            
            # 生成文档ID
            doc_id = IDGenerator.generate_doc_id()
            
            # 创建集合
            await self._ensure_collection_exists(collection_name, has_custom_ids=bool(ids), embedding_type=embedding_type)
            
            # 生成向量
            if not self.embedding_service:
                raise Exception("向量化服务未初始化")
                
            if embedding_type:
                embedding_result = await self.embedding_service.generate_embeddings_batch(
                    texts, embedding_type=embedding_type
                )
            else:
                embedding_result = await self.embedding_service.generate_embeddings_batch(texts)
            
            vectors = embedding_result['data']['vectors']
            vector_time = timer.checkpoint("向量生成")
            
            # 构建记录
            records = []
            for i, (text, vector) in enumerate(zip(texts, vectors)):
                # 处理加密
                content = text
                if encrypt:
                    content = encrypt_text(text)
                
                # 构建元数据
                record_metadata = {
                    "doc_id": doc_id,
                    "text_index": i,
                    "collection": collection_name,
                    "encrypted": encrypt
                }
                
                if metadata and i < len(metadata):
                    record_metadata.update(metadata[i])
                
                # 构建记录
                record = self.record_builder.build_vector_record(
                    content=content,
                    vector=vector,
                    metadata=record_metadata,
                    record_id=ids[i] if ids and i < len(ids) else None,
                    collection=collection_name,
                    encrypted=encrypt
                )
                records.append(record)
            
            # 插入向量
            if not self.vector_repo:
                raise Exception("向量仓库未初始化")
            await self.vector_repo.insert_vectors(collection_name, records)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="文本上传处理",
                collection=collection_name,
                text_count=len(texts),
                doc_id=doc_id,
                encrypted=encrypt,
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.upload_response_builder.text_upload_response(
                doc_id=doc_id,
                text_count=len(texts),
                vector_dimension=len(vectors[0]) if vectors else 768,
                processing_time=f"{elapsed_time['total']:.3f}秒",
                encrypted=encrypt
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="文本上传处理",
                error=e,
                collection=collection_name,
                text_count=len(texts)
            )
            raise Exception(f"文本上传处理失败: {str(e)}")

    async def upload_texts_batch(
        self,
        items: List[Dict[str, Any]],
        collection_name: str,
        global_metadata: Optional[Dict[str, Any]] = None,
        encrypt: bool = False,
        batch_size: int = 10,
        database: Optional[str] = None,
        embedding_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量上传文本项
        
        Args:
            items: 文本项列表，每项包含text、metadata、id等字段
            collection_name: 集合名称
            global_metadata: 全局元数据
            encrypt: 是否加密
            batch_size: 批次大小
            database: 数据库名称
            embedding_type: 向量模型类型
            
        Returns:
            Dict[str, Any]: 批量上传结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("批量文本上传")
        timer.start()
        
        try:
            if not items:
                raise ValueError("文本项列表不能为空")
            
            # 验证批次大小
            batch_size = self.validator.validate_batch_size(batch_size)
            
            # 验证重复ID
            custom_ids = [item.get('id') for item in items if item.get('id') is not None]
            if custom_ids and len(custom_ids) != len(set(custom_ids)):
                raise ValueError("存在重复的自定义ID")
            
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
                self.collection_repo = CollectionRepository(db_connection)
            
            # 生成文档ID
            doc_id = IDGenerator.generate_doc_id()
            
            # 创建集合
            has_custom_ids = any(item.get('id') is not None for item in items)
            await self._ensure_collection_exists(collection_name, has_custom_ids=has_custom_ids, embedding_type=embedding_type)
            
            # 批量处理
            all_records = await self._process_items_batch(
                items=items,
                doc_id=doc_id,
                collection_name=collection_name,
                global_metadata=global_metadata or {},
                encrypt=encrypt,
                batch_size=batch_size,
                embedding_type=embedding_type
            )
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="批量文本上传",
                collection=collection_name,
                item_count=len(items),
                batch_count=len(self.list_processor.chunk_list(items, batch_size)),
                doc_id=doc_id,
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.upload_response_builder.batch_upload_response(
                doc_id=doc_id,
                text_count=len(items),
                batch_count=len(self.list_processor.chunk_list(items, batch_size)),
                batch_size=batch_size,
                vector_dimension=len(all_records[0]['vector']) if all_records else 768,
                processing_time=f"{elapsed_time['total']:.3f}秒",
                avg_time_per_text=f"{elapsed_time['total']/len(items):.3f}秒",
                encrypted=encrypt
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="批量文本上传",
                error=e,
                collection=collection_name,
                item_count=len(items)
            )
            raise Exception(f"批量文本上传失败: {str(e)}")

    async def upload_texts_batch_hybrid(
        self,
        items: List[Dict[str, Any]],
        collection_name: str = "documents_hybrid",
        global_metadata: Optional[Dict[str, Any]] = None,
        encrypt: bool = False,
        batch_size: int = 10,
        database: Optional[str] = None,
        embedding_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量上传文本项为混合检索向量
        
        Args:
            items: 文本项列表
            collection_name: 混合检索集合名称
            global_metadata: 全局元数据
            encrypt: 是否加密
            batch_size: 批次大小
            database: 数据库名称
            embedding_type: 向量模型类型
            
        Returns:
            Dict[str, Any]: 混合检索上传结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("混合检索批量上传")
        timer.start()
        
        try:
            if not items:
                raise ValueError("文本项列表不能为空")
            
            batch_size = self.validator.validate_batch_size(batch_size)
            
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.vector_repo = VectorRepository(db_connection)
                self.collection_repo = CollectionRepository(db_connection)
            
            # 生成文档ID
            doc_id = IDGenerator.generate_doc_id()

            # 1. 根据embedding_type获取模型信息
            model = await self.embedding_service.get_embedding_model(embedding_type)
            dense_vector_dim = model.get_dimension()

            # 创建混合检索集合
            await self._ensure_hybrid_collection_exists(collection_name, dense_vector_dim)
            
            # 批量处理混合检索向量
            all_records = await self._process_items_batch_hybrid(
                items=items,
                doc_id=doc_id,
                collection_name=collection_name,
                global_metadata=global_metadata or {},
                encrypt=encrypt,
                batch_size=batch_size,
                embedding_type=embedding_type
            )
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="混合检索批量上传",
                collection=collection_name,
                item_count=len(items),
                doc_id=doc_id,
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            # 构建响应
            response = self.upload_response_builder.batch_upload_response(
                doc_id=doc_id,
                text_count=len(items),
                batch_count=len(self.list_processor.chunk_list(items, batch_size)),
                batch_size=batch_size,
                vector_dimension=dense_vector_dim,
                processing_time=f"{elapsed_time['total']:.3f}秒",
                avg_time_per_text=f"{elapsed_time['total']/len(items):.3f}秒",
                encrypted=encrypt
            )
            
            # 添加额外信息到响应数据
            if 'data' in response:
                response['data'].update({
                    "collection_type": "hybrid",
                    "dense_vector_dim": dense_vector_dim
                })
            
            return response
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="混合检索批量上传",
                error=e,
                collection=collection_name
            )
            raise Exception(f"混合检索批量上传失败: {str(e)}")

    async def _ensure_collection_exists(self, collection_name: str, has_custom_ids: bool = False, embedding_type: Optional[str] = None):
        """确保集合存在"""
        try:
            # 获取向量维度 - 使用指定的embedding_type
            if embedding_type:
                embedding_result = await self.embedding_service.generate_embedding("test", embedding_type=embedding_type)
            else:
                embedding_result = await self.embedding_service.generate_embedding("test")
            vector_dim = embedding_result['data']['dimension']
            
            await self.collection_repo.create_collection(
                collection_name=collection_name,
                vector_dimension=vector_dim,
                auto_id=not has_custom_ids,
                enable_dynamic_field=True
            )
        except Exception as e:
            if "already exists" not in str(e).lower():
                raise e

    async def _ensure_hybrid_collection_exists(self, collection_name: str, dense_vector_dim: int = 768):
        """确保混合检索集合存在"""
        try:
            await self.collection_repo.create_hybrid_collection(
                collection_name=collection_name,
                dense_vector_dim=dense_vector_dim,
                auto_id=True,
                enable_dynamic_field=True
            )
        except Exception as e:
            if "already exists" not in str(e).lower():
                raise e

    async def _process_chunks_to_vectors(
        self,
        chunks: List[str],
        doc_id: str,
        collection_name: str,
        encrypt: bool,
        embedding_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理分块为向量"""
        start_time = time.time()
        
        # 生成向量
        if embedding_type:
            embedding_result = await self.embedding_service.generate_embeddings_batch(
                chunks, embedding_type=embedding_type
            )
        else:
            embedding_result = await self.embedding_service.generate_embeddings_batch(chunks)
        
        vectors = embedding_result['data']['vectors']
        vector_time = time.time() - start_time
        
        # 构建记录并批量存储
        batch_size = 10
        for i in range(0, len(chunks), batch_size):
            batch_chunks = chunks[i:i+batch_size]
            batch_vectors = vectors[i:i+batch_size]
            
            records = []
            for idx, (chunk, vector) in enumerate(zip(batch_chunks, batch_vectors), start=i):
                # 处理加密
                content = chunk
                if encrypt:
                    content = encrypt_text(chunk)
                
                # 构建元数据
                record_metadata = {
                    "doc_id": doc_id,
                    "chunk_index": idx,
                    "collection": collection_name,
                    "encrypted": encrypt
                }
                if metadata:
                    record_metadata.update(metadata)
                
                record = self.record_builder.build_vector_record(
                    content=content,
                    vector=vector,
                    metadata=record_metadata,
                    collection=collection_name,
                    encrypted=encrypt
                )
                records.append(record)
            
            # 存储当前批次
            await self.vector_repo.insert_vectors(collection_name, records)
            
            # 显示进度
            processed = min(i + batch_size, len(chunks))
            self.progress_logger.log_progress(processed, len(chunks), "向量存储进度")
        
        self.progress_logger.finish_progress()
        
        return {
            "vector_dimension": len(vectors[0]) if vectors else 768,
            "vector_time": vector_time
        }

    async def _process_items_batch(
        self,
        items: List[Dict[str, Any]],
        doc_id: str,
        collection_name: str,
        global_metadata: Dict[str, Any],
        encrypt: bool,
        batch_size: int,
        embedding_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """批量处理文本项"""
        
        async def process_text_item(item: Dict[str, Any], item_index: int) -> Dict[str, Any]:
            """处理单个文本项"""
            text = item.get('text', '')
            if not text:
                raise ValueError(f"文本项 {item_index + 1} 的text字段为空")
            
            # 生成向量
            if embedding_type:
                embedding_result = await self.embedding_service.generate_embedding(
                    text, embedding_type=embedding_type
                )
            else:
                embedding_result = await self.embedding_service.generate_embedding(text)
            
            vector = embedding_result['data']['vector']
            
            # 处理加密
            content = text
            if encrypt:
                content = encrypt_text(text)
            
            # 合并元数据
            combined_metadata = {
                "doc_id": doc_id,
                "text_index": item_index,
                "collection": collection_name,
                "encrypted": encrypt,
                **global_metadata
            }
            
            if item.get('metadata'):
                combined_metadata.update(item['metadata'])
            
            # 构建记录
            record = self.record_builder.build_vector_record(
                content=content,
                vector=vector,
                metadata=combined_metadata,
                record_id=item.get('id'),
                collection=collection_name,
                encrypted=encrypt
            )
            
            # 添加额外字段
            for field, value in item.items():
                if field not in ['text', 'metadata', 'id'] and value is not None:
                    record[field] = value
            
            return record

        async def process_batch(batch_items: List[tuple]) -> List[Dict[str, Any]]:
            """并发处理一个批次"""
            tasks = [process_text_item(item, index) for item, index in batch_items]
            return await asyncio.gather(*tasks)
        
        # 分批处理
        all_records = []
        batches = self.list_processor.chunk_list(
            [(items[i], i) for i in range(len(items))], 
            batch_size
        )
        
        for batch_idx, batch in enumerate(batches):
            self.progress_logger.log_batch_progress(
                batch_idx, len(batches), len(batch), "批量处理进度"
            )
            
            # 并发处理当前批次
            batch_records = await process_batch(batch)
            all_records.extend(batch_records)
            
            # 立即存储当前批次
            await self.vector_repo.insert_vectors(collection_name, batch_records)
        
        return all_records

    async def _process_items_batch_hybrid(
        self,
        items: List[Dict[str, Any]],
        doc_id: str,
        collection_name: str,
        global_metadata: Dict[str, Any],
        encrypt: bool,
        batch_size: int,
        embedding_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """批量处理混合检索文本项"""
        
        async def process_hybrid_batch(batch_items: List[tuple]) -> List[Dict[str, Any]]:
            """批量处理混合检索批次 - 优化版本：先批量生成向量，再构建记录"""
            # 提取批次中的所有文本
            batch_texts = []
            batch_info = []  # 存储 (item, index) 信息
            
            for item, item_index in batch_items:
                text = item.get('text', '')
                if not text:
                    raise ValueError(f"文本项 {item_index + 1} 的text字段为空")
                batch_texts.append(text)
                batch_info.append((item, item_index))
            
            # 批量生成密集向量（关键优化点）
            if embedding_type:
                embedding_result = await self.embedding_service.generate_embeddings_batch(
                    batch_texts, embedding_type=embedding_type
                )
            else:
                embedding_result = await self.embedding_service.generate_embeddings_batch(batch_texts)
            
            dense_vectors = embedding_result['data']['vectors']
            
            # 构建记录
            records = []
            for (item, item_index), dense_vector, text in zip(batch_info, dense_vectors, batch_texts):
                # 处理加密
                content = text
                if encrypt:
                    content = encrypt_text(text)
                
                # 合并元数据
                combined_metadata = {
                    "doc_id": doc_id,
                    "text_index": item_index,
                    "collection": collection_name,
                    "encrypted": encrypt,
                    **global_metadata
                }
                
                if item.get('metadata'):
                    combined_metadata.update(item['metadata'])
                
                # 构建混合检索记录
                record = self.record_builder.build_hybrid_vector_record(
                    content=content,  # content字段已用于全文检索，无需额外的text参数
                    vector=dense_vector,
                    metadata=combined_metadata,
                    record_id=item.get('id'),
                    collection=collection_name,
                    encrypted=encrypt
                )
                records.append(record)
            
            return records
        
        # 分批处理
        all_records = []
        batches = self.list_processor.chunk_list(
            [(items[i], i) for i in range(len(items))], 
            batch_size
        )
        
        for batch_idx, batch in enumerate(batches):
            self.progress_logger.log_batch_progress(
                batch_idx, len(batches), len(batch), "混合检索批处理进度"
            )
            
            # 批量处理当前批次（使用优化后的方法）
            batch_records = await process_hybrid_batch(batch)
            all_records.extend(batch_records)
            
            # 立即存储当前批次
            await self.vector_repo.insert_hybrid_vectors(collection_name, batch_records)
        
        return all_records

    def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        return {
            "initialized": self._initialized,
            "components": {
                "vector_repository": "active" if self.vector_repo else "inactive",
                "collection_repository": "active" if self.collection_repo else "inactive",
                "database_repository": "active",
                "embedding_service": "active" if self.embedding_service else "inactive"
            },
            "capabilities": {
                "document_upload": True,
                "text_upload": True,
                "batch_processing": True,
                "hybrid_search": True,
                "encryption": True,
                "chunking": True,
                "file_types": ["pdf", "docx", "txt"]
            }
        } 