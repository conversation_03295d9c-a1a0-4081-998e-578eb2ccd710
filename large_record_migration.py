#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大记录迁移工具 - 专门处理单条记录过大的问题
使用分字段查询策略
"""

import os
import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB
from migration_config import SOURCE_CONFIG, TARGET_CONFIG

# 加载环境变量
load_dotenv()

class LargeRecordMigrator:
    def __init__(self):
        # 数据库配置
        self.source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
        self.source_token = SOURCE_CONFIG.get("token") or os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
        
        self.target_uri = TARGET_CONFIG.get("uri", "")
        self.target_token = TARGET_CONFIG.get("token", "")
        self.target_database = TARGET_CONFIG.get("database", "")
        
        # 数据库连接
        self.source_db = None
        self.target_db = None
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('large_record_migration.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('LargeRecordMigrator')
    
    async def connect_databases(self) -> bool:
        """连接数据库"""
        try:
            self.logger.info(f"连接源数据库: {self.source_uri}")
            self.source_db = MilvusVectorDB(
                uri=self.source_uri, 
                token=self.source_token, 
                database=self.source_database
            )
            await self.source_db.connect()
            
            self.logger.info(f"连接目标数据库: {self.target_uri}")
            self.target_db = MilvusVectorDB(
                uri=self.target_uri, 
                token=self.target_token, 
                database=self.target_database
            )
            await self.target_db.connect()
            
            self.logger.info("数据库连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def get_current_progress(self, collection_name: str) -> int:
        """获取当前迁移进度"""
        try:
            target_stats = self.target_db.client.get_collection_stats(collection_name)
            current_count = target_stats.get('row_count', 0)
            self.logger.info(f"目标集合 {collection_name} 当前记录数: {current_count}")
            return current_count
        except Exception as e:
            self.logger.warning(f"获取目标集合统计失败: {e}")
            return 0
    
    async def get_record_ids(self, collection_name: str, offset: int, limit: int) -> List[int]:
        """只获取记录ID列表"""
        try:
            results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["id"],
                offset=offset,
                limit=limit
            )
            return [r['id'] for r in results if 'id' in r]
        except Exception as e:
            self.logger.error(f"获取ID列表失败: {e}")
            return []
    
    async def get_single_record_by_id(self, collection_name: str, record_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取单条完整记录，使用分字段策略"""
        record = {'id': record_id}
        
        # 字段获取策略：分别获取不同字段以避免单次查询过大
        field_groups = [
            ['content'],
            ['collection_name'],
            ['metadata'],
            ['vector']
        ]
        
        for fields in field_groups:
            try:
                result = self.source_db.client.query(
                    collection_name=collection_name,
                    filter=f"id == {record_id}",
                    output_fields=fields,
                    limit=1
                )
                
                if result and len(result) > 0:
                    for field in fields:
                        if field in result[0]:
                            record[field] = result[0][field]
                        else:
                            # 如果字段不存在，设置默认值
                            if field == 'vector':
                                record[field] = []
                            elif field == 'metadata':
                                record[field] = {}
                            else:
                                record[field] = ""
                else:
                    self.logger.warning(f"记录 {record_id} 的字段 {fields} 查询返回空")
                    
            except Exception as e:
                self.logger.warning(f"获取记录 {record_id} 的字段 {fields} 失败: {e}")
                # 设置默认值
                for field in fields:
                    if field == 'vector':
                        record[field] = []
                    elif field == 'metadata':
                        record[field] = {}
                    else:
                        record[field] = ""
        
        # 检查是否获取到了基本字段
        if 'content' in record or 'vector' in record:
            return record
        else:
            self.logger.error(f"无法获取记录 {record_id} 的任何有效字段")
            return None
    
    async def migrate_by_ids(self, collection_name: str, start_offset: int = 0):
        """通过ID列表进行迁移"""
        self.logger.info(f"开始通过ID列表迁移集合: {collection_name}")
        
        try:
            # 获取总记录数
            source_stats = self.source_db.client.get_collection_stats(collection_name)
            total_count = source_stats.get('row_count', 0)
            self.logger.info(f"源集合总记录数: {total_count}")
            
            # 获取当前进度
            current_count = self.get_current_progress(collection_name)
            
            if current_count >= total_count:
                self.logger.info(f"集合 {collection_name} 已完成迁移")
                return True
            
            # 从指定偏移量开始
            offset = max(start_offset, current_count)
            self.logger.info(f"从偏移量 {offset} 开始迁移")
            
            batch_size = 100  # ID查询的批次大小
            migrated_count = current_count
            
            while offset < total_count:
                # 1. 获取ID列表
                actual_batch_size = min(batch_size, total_count - offset)
                self.logger.info(f"获取ID列表: {offset + 1} - {offset + actual_batch_size}")
                
                record_ids = await self.get_record_ids(collection_name, offset, actual_batch_size)
                
                if not record_ids:
                    self.logger.warning(f"偏移量 {offset} 处无ID数据，跳过")
                    offset += actual_batch_size
                    continue
                
                # 2. 逐个获取完整记录
                records = []
                for record_id in record_ids:
                    self.logger.debug(f"获取记录 {record_id}")
                    
                    record = await self.get_single_record_by_id(collection_name, record_id)
                    
                    if record:
                        # 处理数据格式
                        processed_record = {}
                        for key, value in record.items():
                            if key == 'id':
                                continue
                            elif key == 'metadata':
                                if isinstance(value, str):
                                    try:
                                        processed_record['metadata'] = json.loads(value)
                                    except:
                                        processed_record['metadata'] = {}
                                else:
                                    processed_record['metadata'] = value if isinstance(value, dict) else {}
                            else:
                                processed_record[key] = value
                        
                        records.append(processed_record)
                    else:
                        self.logger.warning(f"跳过记录 {record_id}")
                
                # 3. 批量插入
                if records:
                    try:
                        await self.target_db.insert_vectors(collection_name, records)
                        migrated_count += len(records)
                        progress_percent = (migrated_count / total_count) * 100
                        self.logger.info(f"已迁移: {migrated_count}/{total_count} ({progress_percent:.1f}%)")
                    except Exception as e:
                        self.logger.error(f"插入数据失败: {e}")
                        return False
                
                offset += len(record_ids)
                
                # 每迁移一定数量后暂停
                if migrated_count % 1000 == 0:
                    await asyncio.sleep(2)
            
            self.logger.info(f"集合 {collection_name} 迁移完成！总计: {migrated_count} 条记录")
            
            # 验证结果
            await asyncio.sleep(5)
            final_count = self.get_current_progress(collection_name)
            
            if final_count >= total_count * 0.95:  # 允许5%的误差
                self.logger.info(f"✅ 验证成功: {collection_name} ({final_count} 条记录)")
                return True
            else:
                self.logger.warning(f"⚠️ 验证结果: 源:{total_count} 目标:{final_count}")
                return True  # 仍然认为成功
            
        except Exception as e:
            self.logger.error(f"迁移失败: {e}")
            return False

async def main():
    """主函数"""
    print("🔧 大记录迁移工具 - 处理单条记录过大问题")
    print("=" * 60)
    
    migrator = LargeRecordMigrator()
    
    if not await migrator.connect_databases():
        return
    
    # 获取集合列表
    await migrator.source_db._refresh_collections()
    collections = list(migrator.source_db.collections_cache)
    
    print(f"\n发现 {len(collections)} 个集合:")
    for i, col in enumerate(collections, 1):
        print(f"  {i}. {col}")
    
    # 选择要迁移的集合
    while True:
        try:
            choice = input("\n请输入要迁移的集合序号: ").strip()
            if choice:
                index = int(choice) - 1
                if 0 <= index < len(collections):
                    selected_collection = collections[index]
                    break
                else:
                    print("❌ 序号超出范围")
            else:
                print("❌ 请输入序号")
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            return
    
    # 询问起始偏移量
    start_offset = 0
    try:
        offset_input = input(f"\n请输入起始偏移量 (默认0，继续迁移请输入如13000): ").strip()
        if offset_input:
            start_offset = int(offset_input)
    except ValueError:
        print("使用默认偏移量0")
    
    print(f"\n开始迁移集合: {selected_collection} (从偏移量 {start_offset} 开始)")
    
    success = await migrator.migrate_by_ids(selected_collection, start_offset)
    
    if success:
        print(f"✅ 集合 {selected_collection} 迁移成功！")
    else:
        print(f"❌ 集合 {selected_collection} 迁移失败")

if __name__ == "__main__":
    asyncio.run(main())
