"""
API请求数据模型定义

包含所有API的请求参数数据结构
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union


class EmbeddingRequest(BaseModel):
    """向量生成请求模型"""
    text: str

    class Config:
        extra = "allow"
        orm_mode = True


class TextUploadRequest(BaseModel):
    """文本列表上传请求模型"""
    texts: List[str] = Field(..., description="要上传的文本列表")
    collection: str = Field("documents", description="文档所属的集合名称，用于区分不同业务场景")
    database: Optional[str] = Field(None, description="要使用的数据库名称，不指定则使用默认数据库")
    metadata: Optional[Dict[str, Any]] = Field(None, description="要附加到所有文本的元数据")
    encrypt: bool = Field(False, description="是否对上传内容进行AES加密")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'azure-openai'，不指定则使用默认方法")
    ids: Optional[List[Union[int, str]]] = Field(None, description="可选的ID列表，用于指定每个文本的向量库ID。如果不提供，则使用自增ID。长度必须与texts列表长度一致")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True


class BatchTextItem(BaseModel):
    """批量文本项模型"""
    text: str = Field(..., description="文本内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="该文本的元数据")
    id: Optional[Union[int, str]] = Field(None, description="可选的自定义ID")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True


class BatchTextUploadRequest(BaseModel):
    """批量文本上传请求模型"""
    items: List[BatchTextItem] = Field(..., description="要上传的文本项列表，每个项包含文本和对应的metadata")
    collection: str = Field("documents", description="文档所属的集合名称，用于区分不同业务场景")
    database: Optional[str] = Field(None, description="要使用的数据库名称，不指定则使用默认数据库")
    global_metadata: Optional[Dict[str, Any]] = Field(None, description="要附加到所有文本的全局元数据，会与每个文本的metadata合并")
    encrypt: bool = Field(False, description="是否对上传内容进行AES加密")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'azure-openai'，不指定则使用默认方法")
    batch_size: int = Field(10, description="并发处理的批次大小，默认为10")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True


class HybridTextUploadRequest(BaseModel):
    """混合检索文本上传请求模型"""
    items: List[BatchTextItem] = Field(..., description="要上传的文本项列表，每个项包含文本和对应的metadata")
    collection: str = Field("documents_hybrid", description="混合检索集合名称，用于区分不同业务场景")
    database: Optional[str] = Field(None, description="要使用的数据库名称，不指定则使用默认数据库")
    global_metadata: Optional[Dict[str, Any]] = Field(None, description="要附加到所有文本的全局元数据，会与每个文本的metadata合并")
    encrypt: bool = Field(False, description="是否对上传内容进行AES加密")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'azure-openai'，不指定则使用默认方法")
    batch_size: int = Field(10, description="并发处理的批次大小，默认为10")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True


class SearchRequest(BaseModel):
    """向量搜索请求模型"""
    text: str = Field(..., description="搜索查询文本")
    top_k: int = Field(5, description="返回结果数量")
    collection: str = Field("documents", description="要搜索的集合名称，可以设置为'all'以搜索所有集合")
    database: str = Field(None, description="要使用的数据库名称，不指定则使用默认数据库")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'openai'，不指定则使用默认方法")
    filter_expr: Optional[str] = Field(None, description="Milvus筛选表达式，例如：\"car_type='passenger'\" 或 \"metadata['category'] == 'electronics'\"")
    metadata_filters: Optional[Dict[str, Any]] = Field(None, description="metadata字段筛选条件，例如：{\"category\": \"electronics\", \"price\": {\"$lt\": 100}}")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True


class DBSearchConfig(BaseModel):
    """数据库搜索配置模型"""
    database: str = Field(..., description="要搜索的数据库名称")
    collections: Optional[List[str]] = Field(None, description="要搜索的集合列表，不指定则搜索该数据库下所有集合")

    class Config:
        extra = "allow"
        orm_mode = True


class MultiDBSearchRequest(BaseModel):
    """多数据库搜索请求模型"""
    text: str = Field(..., description="搜索查询文本")
    top_k: int = Field(5, description="每个数据库返回的结果数量")
    databases: List[Union[str, DBSearchConfig]] = Field(..., description="要搜索的数据库列表，可以是数据库名称字符串或包含数据库名称和集合列表的对象")
    total_results: int = Field(10, description="最终返回的总结果数量")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'openai'，不指定则使用默认方法")
    filter_expr: Optional[str] = Field(None, description="Milvus筛选表达式，例如：\"car_type='passenger'\" 或 \"metadata['category'] == 'electronics'\"")
    metadata_filters: Optional[Dict[str, Any]] = Field(None, description="metadata字段筛选条件，例如：{\"category\": \"electronics\", \"price\": {\"$lt\": 100}}")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True


class HybridMultiDBSearchRequest(BaseModel):
    """混合检索多数据库搜索请求模型"""
    text: str = Field(..., description="搜索查询文本")
    top_k: int = Field(5, description="每个数据库返回的结果数量")
    databases: List[Union[str, DBSearchConfig]] = Field(..., description="要搜索的数据库列表，可以是数据库名称字符串或包含数据库名称和集合列表的对象")
    total_results: int = Field(10, description="最终返回的总结果数量")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'openai'，不指定则使用默认方法")
    filter_expr: Optional[str] = Field(None, description="Milvus筛选表达式，例如：\"car_type='passenger'\" 或 \"metadata['category'] == 'electronics'\"")
    metadata_filters: Optional[Dict[str, Any]] = Field(None, description="metadata字段筛选条件，例如：{\"category\": \"electronics\", \"price\": {\"$lt\": 100}}")
    
    # 混合检索特有参数
    search_strategy: str = Field("hybrid", description="检索策略：'semantic'(仅语义), 'full_text'(仅全文), 'hybrid'(混合)")
    rerank_strategy: str = Field("rrf", description="重排序策略：'rrf'(倒数排名融合) 或 'weighted'(加权融合)")
    
    # RRF重排序参数
    rrf_k: int = Field(60, description="RRF平滑参数k，范围[10,100]，仅在rerank_strategy='rrf'时使用")
    
    # WeightedRanker重排序参数  
    dense_weight: float = Field(0.6, description="语义检索权重(0-1)，仅在rerank_strategy='weighted'时使用")
    sparse_weight: float = Field(0.4, description="全文检索权重(0-1)，仅在rerank_strategy='weighted'时使用")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True



class UpsertRequest(BaseModel):
    """向量更新插入请求模型"""
    records: List[Dict[str, Any]] = Field(..., description="要更新或插入的记录列表，必须包含主键字段。如果记录包含'content'或'text'字段但没有'vector'字段，将自动生成向量")
    collection: str = Field("documents", description="集合名称")
    database: Optional[str] = Field(None, description="要使用的数据库名称，不指定则使用默认数据库")
    auto_flush: bool = Field(True, description="是否自动刷新数据以确保立即生效")
    embedding_type: Optional[str] = Field(None, description="向量生成方法，可选值：'huggingface'或'azure-openai'，不指定则使用默认方法。仅在需要自动生成向量时有效")
    encrypt: bool = Field(False, description="是否对上传内容进行AES加密")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True


class DeleteRequest(BaseModel):
    """向量删除请求模型"""
    collection: str = Field("documents", description="集合名称")
    database: Optional[str] = Field(None, description="要使用的数据库名称，不指定则使用默认数据库")
    ids: Optional[List[Any]] = Field(None, description="要删除的主键ID列表")
    filter_expr: Optional[str] = Field(None, description="删除条件表达式，例如 \"color in ['red', 'blue']\"")
    auto_flush: bool = Field(True, description="是否自动刷新数据以确保立即生效")

    class Config:
        extra = "allow"
        orm_mode = True
        allow_population_by_field_name = True
        validate_assignment = True 