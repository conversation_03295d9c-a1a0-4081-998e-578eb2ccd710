"""
数据库管理服务

负责：
- 数据库生命周期管理
- 集合管理和配置
- 数据库连接协调
- 初始化和设置
"""

import time
from typing import Optional, Dict, Any, List
from app.config.settings import VectorDBConfig
from app.repositories import (
    VectorDatabaseRepository,
    CollectionRepository
)
from app.utils.logging_utils import OperationLogger
from app.utils.validation import ParameterValidator
from app.utils.response_utils import ResponseBuilder


class DatabaseService:
    """数据库管理服务"""
    
    def __init__(self):
        """初始化数据库管理服务"""
        self.db_repo = VectorDatabaseRepository()
        self.collection_repo: Optional[CollectionRepository] = None
        self.operation_logger = OperationLogger()
        self.validator = ParameterValidator()
        self.response_builder = ResponseBuilder()
        self._initialized = False
    
    async def initialize(self, config: VectorDBConfig) -> Dict[str, Any]:
        """
        初始化数据库服务
        
        Args:
            config: 数据库配置
            
        Returns:
            Dict[str, Any]: 初始化结果
        """
        start_time = time.time()
        
        try:
            # 设置数据库配置
            self.db_repo.set_config(config)
            
            # 测试连接
            test_db = await self.db_repo.get_database_connection()
            
            # 初始化collection repository
            self.collection_repo = CollectionRepository(test_db)
            
            self._initialized = True
            elapsed_time = time.time() - start_time
            
            self.operation_logger.log_operation_success(
                operation_name="数据库服务初始化",
                details=f"数据库类型: {config.db_type}, 耗时: {elapsed_time:.3f}秒"
            )
            
            return self.response_builder.success_response(
                message="数据库服务初始化成功",
                data={
                    "db_type": config.db_type,
                    "initialization_time": elapsed_time,
                    "status": "ready"
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="数据库服务初始化",
                error=e
            )
            raise Exception(f"数据库服务初始化失败: {str(e)}")
    
    def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            raise Exception("数据库服务未初始化，请先调用initialize方法")
    
    async def get_database_connection(
        self, 
        database: Optional[str] = None,
        create_if_not_exists: bool = False
    ) -> Any:
        """获取数据库连接"""
        self._ensure_initialized()
        
        try:
            connection = await self.db_repo.get_database_connection(
                database=database,
                create_if_not_exists=create_if_not_exists
            )
            
            # 更新collection repository的连接
            self.collection_repo = CollectionRepository(connection)
            
            return connection
            
        except Exception as e:
            raise Exception(f"获取数据库连接失败: {str(e)}")
    
    async def create_collection(
        self,
        collection_name: str,
        vector_dimension: int,
        database: Optional[str] = None,
        auto_id: bool = True,
        enable_dynamic_field: bool = True
    ) -> Dict[str, Any]:
        """创建普通向量集合"""
        self._ensure_initialized()
        
        try:
            # 获取数据库连接
            if database:
                await self.get_database_connection(database, create_if_not_exists=True)
            
            start_time = time.time()
            
            # 创建集合
            if not self.collection_repo:
                raise Exception("集合仓库未初始化")
                
            await self.collection_repo.create_collection(
                collection_name=collection_name,
                vector_dimension=vector_dimension,
                auto_id=auto_id,
                enable_dynamic_field=enable_dynamic_field
            )
            
            elapsed_time = time.time() - start_time
            
            return self.response_builder.success_response(
                message=f"集合 '{collection_name}' 创建成功",
                data={
                    "collection_name": collection_name,
                    "vector_dimension": vector_dimension,
                    "creation_time": elapsed_time,
                    "status": "created"
                }
            )
            
        except Exception as e:
            raise Exception(f"创建集合失败: {str(e)}")
    
    async def create_hybrid_collection(
        self,
        collection_name: str,
        dense_vector_dim: int,
        database: Optional[str] = None,
        auto_id: bool = True,
        enable_dynamic_field: bool = True
    ) -> Dict[str, Any]:
        """创建混合检索集合"""
        self._ensure_initialized()
        
        try:
            # 获取数据库连接
            if database:
                await self.get_database_connection(database, create_if_not_exists=True)
            
            start_time = time.time()
            
            # 创建混合检索集合
            if not self.collection_repo:
                raise Exception("集合仓库未初始化")
                
            await self.collection_repo.create_hybrid_collection(
                collection_name=collection_name,
                dense_vector_dim=dense_vector_dim,
                auto_id=auto_id,
                enable_dynamic_field=enable_dynamic_field
            )
            
            elapsed_time = time.time() - start_time
            
            return self.response_builder.success_response(
                message=f"混合检索集合 '{collection_name}' 创建成功",
                data={
                    "collection_name": collection_name,
                    "dense_vector_dim": dense_vector_dim,
                    "collection_type": "hybrid",
                    "creation_time": elapsed_time,
                    "status": "created"
                }
            )
            
        except Exception as e:
            raise Exception(f"创建混合检索集合失败: {str(e)}")
    
    def close_connections(self):
        """关闭所有数据库连接"""
        try:
            self.db_repo.close_all_connections()
        except Exception as e:
            pass
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        connection_info = self.db_repo.get_connection_info()
        
        return {
            "initialized": self._initialized,
            "connections": connection_info,
            "repositories": {
                "database_repository": "active",
                "collection_repository": "active" if self.collection_repo else "inactive"
            }
        } 