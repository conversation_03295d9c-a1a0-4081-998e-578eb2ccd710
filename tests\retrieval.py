import requests

def retrieval_knowledge(query: str, 
                        databases: list[dict], 
                        embedding_type: str = None, 
                        top_k: int = 5, 
                        total_results: int = 5) -> list[dict]:
    """根据问题和要筛选的top_k，检索相关的知识"""
    payload = {
        "text": query,
        "top_k": top_k,
        "total_results": total_results,
        "databases": databases,
        "embedding_type": embedding_type,
        "search_strategy": "hybrid",
        "rerank_strategy": "rrf",
        "rrf_k": 50,  # RRF平滑参数
    }
    headers = {"Content-Type": "application/json"}
    url = "http://copilot.csvw.com/rag_service/api/v1/multi_search_hybrid"
    
    try:
        response = requests.post(url, json=payload, headers=headers)
        response_json = response.json()
        print(response_json)
        results = response_json['data'].get('results', [])

    except Exception as e:
        print(f"❌向量检索报错: {e}")
        results = []

    return results


if __name__ == "__main__":
    databases = [
            {
                "database": "CSKB_EmpZero",
                "collections": ["doc"]
            }
        ]
    embedding_type = 'azure-openai'
    top_k = 5
    total_results = 3
    query = "ID.6 X的车机系统与ID.4 X一样么"
    result = retrieval_knowledge(query, databases, embedding_type, top_k, total_results)
    print(result)