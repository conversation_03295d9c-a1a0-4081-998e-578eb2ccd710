"""
结果格式化工具模块
提供统一的结果格式化功能
"""
import json
import time
from typing import List, Dict, Any, Optional
from app.core.encryption import decrypt_text


class TimeFormatter:
    """时间格式化工具"""
    
    @staticmethod
    def format_duration(duration_seconds: float) -> str:
        """
        格式化时长
        
        Args:
            duration_seconds: 时长（秒）
            
        Returns:
            str: 格式化后的时长字符串
        """
        return f"{duration_seconds:.3f}秒"
    
    @staticmethod
    def format_current_time() -> str:
        """
        格式化当前时间
        
        Returns:
            str: 格式化后的时间字符串
        """
        return time.strftime('%H:%M:%S.%f')[:-3]
    
    @staticmethod
    def calculate_percentage(current: int, total: int) -> str:
        """
        计算百分比
        
        Args:
            current: 当前进度
            total: 总数
            
        Returns:
            str: 百分比字符串
        """
        if total == 0:
            return "0.0%"
        return f"{(current/total*100):.1f}%"


class SearchResultFormatter:
    """搜索结果格式化器"""
    
    @staticmethod
    def format_single_result(result: Dict[str, Any], include_decryption: bool = True) -> Dict[str, Any]:
        """
        格式化单个搜索结果
        
        Args:
            result: 原始搜索结果
            include_decryption: 是否包含解密处理
            
        Returns:
            Dict: 格式化后的结果
        """
        formatted_result = {
            "content": result.get('content', ''),
            "similarity": result.get('distance', 0),  # 使用distance作为similarity
            "collection": result.get('collection', ''),
        }
        
        # 添加数据库信息（如果有）
        if 'database' in result:
            formatted_result['database'] = result['database']
        
        # 处理元数据
        metadata = result.get('metadata', {})
        
        # 解密处理
        if include_decryption and isinstance(metadata, dict):
            is_encrypted = metadata.get('encrypted', False)
            if is_encrypted:
                try:
                    print(f"[格式化] 内容已加密，尝试解密")
                    formatted_result['content'] = decrypt_text(formatted_result['content'])
                    print(f"[格式化] 解密成功")
                except Exception as decrypt_error:
                    print(f"[格式化] 解密失败: {decrypt_error}")
                    metadata['decryption_failed'] = True
        
        # 处理元数据字符串解析
        if isinstance(metadata, str):
            try:
                metadata = json.loads(metadata)
            except json.JSONDecodeError:
                print(f"[格式化] 元数据解析失败")
        
        if metadata:
            formatted_result['metadata'] = metadata
        
        # 添加其他字段（排除标准字段和内部字段）
        excluded_fields = {'content', 'collection', 'metadata', 'distance', 'vector', 'database'}
        for key, value in result.items():
            if key not in excluded_fields:
                formatted_result[key] = value
        
        return formatted_result
    
    @staticmethod
    def format_search_results(results: List[Dict[str, Any]], include_decryption: bool = True) -> List[Dict[str, Any]]:
        """
        格式化搜索结果列表
        
        Args:
            results: 原始搜索结果列表
            include_decryption: 是否包含解密处理
            
        Returns:
            List[Dict]: 格式化后的结果列表
        """
        formatted_results = []
        
        for i, result in enumerate(results):
            try:
                formatted_result = SearchResultFormatter.format_single_result(result, include_decryption)
                formatted_results.append(formatted_result)
            except Exception as format_error:
                print(f"[格式化] 处理结果 {i+1} 时出错: {format_error}")
                print(f"[格式化] 原始结果: {result}")
                # 添加错误标记的结果
                error_result = {
                    "content": "结果格式化失败",
                    "similarity": 0,
                    "collection": result.get('collection', ''),
                    "error": str(format_error)
                }
                formatted_results.append(error_result)
        
        return formatted_results
    
    @staticmethod
    def format_hybrid_search_result(result: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化混合检索结果
        
        Args:
            result: 原始混合检索结果
            
        Returns:
            Dict: 格式化后的混合检索结果
        """
        formatted_result = SearchResultFormatter.format_single_result(result)
        
        # 添加混合检索特有字段
        hybrid_fields = ['search_type', 'search_strategy', 'rerank_strategy', 'note']
        for field in hybrid_fields:
            if field in result:
                formatted_result[field] = result[field]
        
        return formatted_result


class ResponseFormatter:
    """响应格式化器"""
    
    @staticmethod
    def format_upload_response(
        doc_id: str,
        count: int,
        vector_dimension: int,
        processing_time: float,
        additional_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        格式化上传响应
        
        Args:
            doc_id: 文档ID
            count: 处理数量
            vector_dimension: 向量维度
            processing_time: 处理时间
            additional_info: 额外信息
            
        Returns:
            Dict: 格式化后的响应
        """
        response = {
            "doc_id": doc_id,
            "count": count,
            "vector_dimension": vector_dimension,
            "processing_time": TimeFormatter.format_duration(processing_time)
        }
        
        if additional_info:
            response.update(additional_info)
        
        return response
    
    @staticmethod
    def format_search_response(
        query: str,
        results: List[Dict[str, Any]],
        search_time: float,
        additional_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        格式化搜索响应
        
        Args:
            query: 查询文本
            results: 搜索结果
            search_time: 搜索时间
            additional_info: 额外信息
            
        Returns:
            Dict: 格式化后的响应
        """
        response = {
            "query": query,
            "results": results,
            "total_results": len(results),
            "search_time": TimeFormatter.format_duration(search_time)
        }
        
        if additional_info:
            response.update(additional_info)
        
        return response
    
    @staticmethod
    def format_error_response(error_message: str, error_code: Optional[str] = None) -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            error_message: 错误消息
            error_code: 错误代码
            
        Returns:
            Dict: 格式化后的错误响应
        """
        response = {
            "error": error_message,
            "success": False
        }
        
        if error_code:
            response["error_code"] = error_code
        
        return response
    
    @staticmethod
    def format_batch_response(
        doc_id: str,
        total_count: int,
        batch_count: int,
        batch_size: int,
        processing_time: float,
        vector_dimension: int,
        additional_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        格式化批量处理响应
        
        Args:
            doc_id: 文档ID
            total_count: 总处理数量
            batch_count: 批次数量
            batch_size: 批次大小
            processing_time: 处理时间
            vector_dimension: 向量维度
            additional_info: 额外信息
            
        Returns:
            Dict: 格式化后的响应
        """
        avg_time_per_item = processing_time / total_count if total_count > 0 else 0
        
        response = {
            "doc_id": doc_id,
            "text_count": total_count,
            "batch_count": batch_count,
            "batch_size": batch_size,
            "vector_dimension": vector_dimension,
            "processing_time": TimeFormatter.format_duration(processing_time),
            "avg_time_per_text": TimeFormatter.format_duration(avg_time_per_item)
        }
        
        if additional_info:
            response.update(additional_info)
        
        return response


class ProgressFormatter:
    """进度格式化器"""
    
    @staticmethod
    def format_progress(current: int, total: int, prefix: str = "处理进度") -> str:
        """
        格式化进度信息
        
        Args:
            current: 当前进度
            total: 总数
            prefix: 前缀信息
            
        Returns:
            str: 格式化后的进度字符串
        """
        percentage = TimeFormatter.calculate_percentage(current, total)
        return f"{prefix}: [{current}/{total}] {percentage}"
    
    @staticmethod
    def format_batch_progress(batch_idx: int, total_batches: int, batch_size: int) -> str:
        """
        格式化批次进度
        
        Args:
            batch_idx: 当前批次索引
            total_batches: 总批次数
            batch_size: 批次大小
            
        Returns:
            str: 格式化后的批次进度字符串
        """
        return f"处理批次 {batch_idx + 1}/{total_batches}，包含 {batch_size} 个项目"


class MetadataFormatter:
    """元数据格式化器"""
    
    @staticmethod
    def parse_metadata_string(metadata_str: str) -> Dict[str, Any]:
        """
        解析元数据字符串
        
        Args:
            metadata_str: 元数据字符串
            
        Returns:
            Dict: 解析后的元数据
        """
        try:
            return json.loads(metadata_str)
        except json.JSONDecodeError as e:
            print(f"[元数据] 解析失败: {e}")
            return {"raw_metadata": metadata_str, "parse_error": str(e)}
    
    @staticmethod
    def combine_metadata(global_metadata: Dict[str, Any], item_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并全局元数据和项目元数据
        
        Args:
            global_metadata: 全局元数据
            item_metadata: 项目元数据
            
        Returns:
            Dict: 合并后的元数据
        """
        combined = global_metadata.copy()
        if item_metadata:
            combined.update(item_metadata)
        return combined


class DebugFormatter:
    """调试信息格式化器"""
    
    @staticmethod
    def format_time_breakdown(time_details: Dict[str, float]) -> Dict[str, str]:
        """
        格式化时间分解信息
        
        Args:
            time_details: 时间详情字典
            
        Returns:
            Dict: 格式化后的时间详情
        """
        formatted = {}
        total_time = time_details.get('total', 0)
        
        for key, duration in time_details.items():
            formatted[key] = TimeFormatter.format_duration(duration)
            if key != 'total' and total_time > 0:
                percentage = (duration / total_time * 100)
                formatted[f"{key}_percentage"] = f"{percentage:.1f}%"
        
        return formatted
    
    @staticmethod
    def format_result_summary(results: List[Dict[str, Any]], max_preview: int = 3) -> List[str]:
        """
        格式化结果摘要
        
        Args:
            results: 结果列表
            max_preview: 最大预览数量
            
        Returns:
            List[str]: 格式化后的摘要列表
        """
        summaries = []
        
        for i, result in enumerate(results[:max_preview]):
            content = result.get('content', '')[:50]
            similarity = result.get('similarity', 0)
            collection = result.get('collection', '')
            
            summary = f"结果 {i+1}: '{content}...', 相似度: {similarity:.3f}, 集合: {collection}"
            summaries.append(summary)
        
        if len(results) > max_preview:
            summaries.append(f"... 还有 {len(results) - max_preview} 条结果")
        
        return summaries 