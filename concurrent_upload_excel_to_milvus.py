import asyncio
import aiohttp
import pandas as pd
from pathlib import Path
import time
from typing import List, Dict, Any


# 服务地址
API_URL = "http://copilot.csvw.com/rag_service/api/v1/upload_texts"

# 并发数量
CONCURRENT_LIMIT = 10


async def upload_single_text(session: aiohttp.ClientSession, request_data: Dict[str, Any], index: int, total_rows: int) -> Dict[str, Any]:
    """
    上传单条文本到向量库
    
    Args:
        session: aiohttp会话
        request_data: 请求数据
        index: 当前处理的行索引
        total_rows: 总行数
    
    Returns:
        包含上传结果的字典
    """
    try:
        start_time = time.time()
        async with session.post(API_URL, json=request_data) as response:
            response_text = await response.text()
            status_code = response.status_code
            
            result = {
                "index": index,
                "status_code": status_code,
                "success": status_code == 200,
                "response": response_text,
                "time_taken": time.time() - start_time
            }
            
            if status_code == 200:
                try:
                    result["data"] = await response.json()
                except:
                    result["data"] = {}
            
            return result
    except Exception as e:
        return {
            "index": index,
            "status_code": 0,
            "success": False,
            "response": f"请求出错: {str(e)}",
            "time_taken": time.time() - start_time
        }


async def process_batch(session: aiohttp.ClientSession, batch_data: List[Dict[str, Any]], start_index: int, total_rows: int) -> List[Dict[str, Any]]:
    """
    并发处理一批数据
    
    Args:
        session: aiohttp会话
        batch_data: 一批请求数据
        start_index: 批次起始索引
        total_rows: 总行数
    
    Returns:
        包含所有上传结果的列表
    """
    tasks = []
    for i, request_data in enumerate(batch_data):
        current_index = start_index + i
        tasks.append(upload_single_text(session, request_data, current_index, total_rows))
    
    return await asyncio.gather(*tasks)


async def upload_excel_to_milvus_concurrent():
    print("开始并发处理Excel文件并上传到Milvus向量库...")
    print(f"并发数量: {CONCURRENT_LIMIT}")

    # 数据库和集合配置
    database = "SVWServiceTest"
    collection = "FaqRewriteQuestionIndexV1"

    # 查找data目录下的所有Excel文件
    data_dir = Path("app/data")
    if not data_dir.exists():
        print(f"目录 {data_dir} 不存在，正在创建...")
        data_dir.mkdir(exist_ok=True)
        print(f"请将Excel文件放入 {data_dir} 目录后重新运行脚本")
        return

    excel_files = list(data_dir.glob("*.xlsx")) + list(data_dir.glob("*.xls"))

    if not excel_files:
        print(f"在 {data_dir} 目录下未找到Excel文件")
        return

    # 使用找到的第一个Excel文件
    excel_file = excel_files[0]
    print(f"使用Excel文件: {excel_file}")

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"成功读取Excel文件，共 {len(df)} 行数据")

        # 检查是否有问题和答案列
        question_col = None
        answer_col = None

        # 查找问题和答案列
        for col in df.columns:
            col_lower = str(col).lower()
            if "question" in col_lower or "问题" in col_lower:
                question_col = col
            elif "answer" in col_lower or "答案" in col_lower:
                answer_col = col

        # 如果没有找到，尝试使用前两列
        if question_col is None and len(df.columns) >= 1:
            question_col = df.columns[0]
        if answer_col is None and len(df.columns) >= 2:
            answer_col = df.columns[1]

        if question_col is None or answer_col is None:
            print(f"无法识别问题和答案列，请确保Excel文件包含这些列")
            print(f"可用列: {list(df.columns)}")
            return

        print(f"使用列 '{question_col}' 作为问题列")
        print(f"使用列 '{answer_col}' 作为答案列")

        # 准备所有请求数据
        all_requests = []
        total_rows = len(df)
        skipped_rows = 0

        for index, row in df.iterrows():
            question = str(row[question_col]).strip()
            answer = str(row[answer_col]).strip()

            # 跳过空问题
            if not question or question.lower() == "nan":
                print(f"跳过第 {index+1} 行: 问题为空")
                skipped_rows += 1
                continue

            # 如果答案为空，设置默认值
            if not answer or answer.lower() == "nan":
                answer = "暂无答案"

            # 准备基本元数据
            metadata = {
                "question": question,
                "answer": answer,
                "source": f"Excel导入: {excel_file.name}",
                "row_index": int(index),
                "source_type": "excel_import"
            }

            # 准备请求数据
            request_data = {
                "texts": [question],  # 只使用问题作为向量文本
                "database": database,
                "collection": collection,
                "encrypt": False,  # 不加密内容
                # "embedding_type": "azure-openai",  # 使用Azure OpenAI生成向量
                "metadata": metadata
            }

            # 处理所有其他列作为动态字段
            processed_cols = [question_col, answer_col]

            for col in df.columns:
                if col in processed_cols:
                    continue

                # 获取列值
                value = row[col]

                # 跳过空值
                if pd.isna(value):
                    continue

                # 处理不同类型的值
                if isinstance(value, (int, float)) and not pd.isna(value):
                    # 对于数值类型，保持原始类型
                    field_value = value
                else:
                    # 对于其他类型，转换为字符串
                    field_value = str(value).strip()

                    # 如果是空字符串，跳过
                    if not field_value:
                        continue

                # 将列名转换为合适的字段名（去除空格，转为小写）
                field_name = str(col).strip().lower().replace(" ", "_")

                # 添加到元数据和请求数据中
                metadata[field_name] = field_value
                request_data[field_name] = field_value

            all_requests.append(request_data)

        # 开始并发处理
        print(f"\n准备上传 {len(all_requests)} 条数据 (跳过了 {skipped_rows} 条空数据)")
        
        start_time = time.time()
        success_count = 0
        error_count = 0
        
        # 创建aiohttp会话
        async with aiohttp.ClientSession() as session:
            # 按批次处理数据
            for i in range(0, len(all_requests), CONCURRENT_LIMIT):
                batch = all_requests[i:i+CONCURRENT_LIMIT]
                print(f"\n处理批次 {i//CONCURRENT_LIMIT + 1}/{(len(all_requests)-1)//CONCURRENT_LIMIT + 1} ({i+1}-{min(i+len(batch), len(all_requests))}/{len(all_requests)})")
                
                # 并发处理当前批次
                results = await process_batch(session, batch, i, len(all_requests))
                
                # 处理结果
                for result in results:
                    index = result["index"]
                    if result["success"]:
                        success_count += 1
                        doc_id = result.get("data", {}).get("doc_id", "未知")
                        print(f"✓ 第 {index+1} 行上传成功，文档ID: {doc_id}，耗时: {result['time_taken']:.2f}秒")
                    else:
                        error_count += 1
                        print(f"✗ 第 {index+1} 行上传失败: {result['response'][:100]}...")
                
                # 显示进度
                progress = min((i + len(batch)) / len(all_requests) * 100, 100)
                print(f"当前进度: {progress:.1f}%")
                print(f"成功: {success_count}, 失败: {error_count}, 总计: {success_count + error_count}/{len(all_requests)}")
        
        # 打印最终统计信息
        total_time = time.time() - start_time
        print(f"\n所有数据处理完成! 总耗时: {total_time:.2f}秒")
        print(f"成功上传: {success_count} 条")
        print(f"失败数量: {error_count} 条")
        print(f"平均每条耗时: {total_time/len(all_requests):.2f}秒")
        if success_count > 0:
            print(f"并发效率提升: 约 {CONCURRENT_LIMIT * (success_count / len(all_requests)):.1f}倍")

    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(upload_excel_to_milvus_concurrent())
