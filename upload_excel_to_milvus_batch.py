import asyncio
import os
import aiohttp
import pandas as pd
from dotenv import load_dotenv
from pathlib import Path
from aiohttp import ClientSession

API_URL = "http://copilot.csvw.com/rag_service/api/v1/upload_texts"

async def upload_excel_to_milvus():
    print("开始处理Excel文件并上传到Milvus向量库...")

    database = "SVWServiceTest"
    collection = "FaqRewriteQuestionIndex"
    semaphore = asyncio.Semaphore(10)
    timeout = aiohttp.ClientTimeout(total=60)

    data_dir = Path("app/data")
    if not data_dir.exists():
        print(f"目录 {data_dir} 不存在，正在创建...")
        data_dir.mkdir(exist_ok=True)
        print(f"请将Excel文件放入 {data_dir} 目录后重新运行脚本")
        return

    excel_files = list(data_dir.glob("*.xlsx")) + list(data_dir.glob("*.xls"))
    if not excel_files:
        print(f"在 {data_dir} 目录下未找到Excel文件")
        return

    excel_file = excel_files[0]
    print(f"使用Excel文件: {excel_file}")

    try:
        df = pd.read_excel(excel_file)
        print(f"成功读取Excel文件，共 {len(df)} 行数据")

        question_col = None
        answer_col = None
        for col in df.columns:
            col_lower = str(col).lower()
            if "question" in col_lower:
                question_col = col
            elif "answer" in col_lower:
                answer_col = col

        if question_col is None and len(df.columns) >= 1:
            question_col = df.columns[0]
        if answer_col is None and len(df.columns) >= 2:
            answer_col = df.columns[1]

        if question_col is None or answer_col is None:
            print(f"无法识别问题和答案列")
            print(f"可用列: {list(df.columns)}")
            return

        print(f"问题列: '{question_col}', 答案列: '{answer_col}'")

        async def process_row(row, index):
            async with semaphore:
                question = str(row[question_col]).strip()
                answer = str(row[answer_col]).strip()

                if not question or question.lower() == "nan":
                    print(f"跳过第 {index+1} 行: 问题为空")
                    return None

                answer = answer if answer and answer.lower() != "nan" else "暂无答案"

                metadata = {
                    "question": question,
                    "answer": answer,
                    "source": f"Excel导入: {excel_file.name}",
                    "row_index": int(index),
                    "source_type": "excel_import"
                }

                request_data = {
                    "texts": [question],
                    "database": database,
                    "collection": collection,
                    "encrypt": False,
                    "metadata": metadata
                }

                processed_cols = [question_col, answer_col]
                for col in df.columns:
                    if col in processed_cols:
                        continue
                    value = row[col]
                    if pd.isna(value):
                        continue

                    if isinstance(value, (int, float)):
                        field_value = value
                    else:
                        field_value = str(value).strip()
                        if not field_value:
                            continue

                    field_name = str(col).strip().lower().replace(" ", "_")
                    metadata[field_name] = field_value
                    request_data[field_name] = field_value

                try:
                    async with ClientSession(timeout=timeout) as session:
                        response = await session.post(API_URL, json=request_data)
                        if response.status == 200:
                            result = await response.json()
                            return True
                        return False
                except Exception as e:
                    print(f"请求异常: {e}")
                    return False

        tasks = []
        for index, row in df.iterrows():
            tasks.append(asyncio.create_task(process_row(row, index)))

        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r is True)
        error_count = len(results) - success_count

        print(f"\n处理完成! 成功: {success_count}, 失败: {error_count}")

    except Exception as e:
        print(f"处理异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(upload_excel_to_milvus())