from typing import Any, Dict
from fastapi import Depends
from app.config.settings import ModelConfig, VectorDBConfig
from app.core.embedding import EmbeddingModel
from app.core.vectordb import VectorDatabase
import httpx
import time

# 全局单例对象
_embedding_model = None
_vector_db = None
_model_load_time = 0

async def get_model_config() -> ModelConfig:
    return ModelConfig()

def get_model_config_sync() -> ModelConfig:
    try:
        config = ModelConfig()
        print(f"[成功] 成功加载模型配置")
        return config
    except Exception as e:
        print(f"[错误] 加载模型配置失败: {e}")
        raise

async def get_vectordb_config() -> VectorDBConfig:
    return VectorDBConfig()

def get_vectordb_config_sync() -> VectorDBConfig:
    try:
        config = VectorDBConfig()
        print(f"[成功] 成功加载向量数据库配置")
        return config
    except Exception as e:
        print(f"[错误] 加载向量数据库配置失败: {e}")
        raise

async def get_embed_model_sync(embedding_type: str = None) -> EmbeddingModel:
    """
    获取嵌入模型实例

    Args:
        embedding_type: 嵌入模型类型，可选值：
            - None: 使用配置文件中的默认模型
            - "huggingface": 使用HuggingFace模型
            - "azure-openai": 使用Azure OpenAI API模型

    Returns:
        EmbeddingModel实例
    """
    global _embedding_model, _model_load_time

    # 如果指定了embedding_type，则创建新的模型实例
    if embedding_type:
        print(f"[加载模型] 使用指定的嵌入模型类型: {embedding_type}")
        
        from dotenv import load_dotenv

        # 确保加载.env文件
        load_dotenv()

        if embedding_type.lower() == "azure-openai":
            from app.core.embedding import AzureOpenAIEmbedding

            # 创建Azure OpenAI Embedding模型
            # 不再进行重复校验，让AzureOpenAIEmbedding类自己处理校验
            try:
                model = AzureOpenAIEmbedding(
                    model_name="text-embedding-3-large"  # 使用大模型获取更好的效果
                )

                print("[成功] 成功创建Azure OpenAI Embedding模型")
            except ValueError as e:
                # 捕获并重新抛出校验错误，添加更多上下文信息
                print(f"[错误] 初始化Azure OpenAI Embedding模型失败: {str(e)}")
                raise ValueError(f"初始化Azure OpenAI Embedding模型失败: {str(e)}")

            # 预热模型
            _ = model.generate("Hello world")

            return model

        elif embedding_type.lower() == "qwen3":
            from app.core.embedding import Qwen3Embedding

            # 创建Qwen3 Embedding模型
            try:
                model = Qwen3Embedding(
                    model_name="text-embedding-v4"  # 使用大模型获取更好的效果
                )

                print("[成功] 成功创建Qwen3 Embedding模型")
            except ValueError as e:
                # 捕获并重新抛出校验错误，添加更多上下文信息
                print(f"[错误] 初始化Qwen3 Embedding模型失败: {str(e)}")
                raise ValueError(f"初始化Qwen3 Embedding模型失败: {str(e)}")

            # 预热模型
            _ = model.generate("Hello world")

            return model

        elif embedding_type.lower() == "bge-m3":
            from app.core.embedding import BgeM3Embedding

            # 创建Bge-m3 Embedding模型
            try:
                model = BgeM3Embedding(
                    model_name="embedding" 
                )

                print("[成功] 成功创建Bge-m3 Embedding模型")
            except ValueError as e:
                # 捕获并重新抛出校验错误，添加更多上下文信息
                print(f"[错误] 初始化Bge-m3 Embedding模型失败: {str(e)}")
                raise ValueError(f"初始化Bge-m3 Embedding模型失败: {str(e)}")

            # 预热模型
            _ = model.generate("Hello world")

            return model

        elif embedding_type.lower() == "huggingface":
            from app.core.embedding import HuggingFaceEmbedding
            cfg = get_model_config_sync()

            # 创建HuggingFace Embedding模型
            model = HuggingFaceEmbedding(cfg.model_name, cfg.device, cfg.model_path, cfg.cache_dir)

            # 预热模型
            _ = model.generate("Hello world")

            return model

        else:
            raise ValueError(f"不支持的嵌入模型类型: {embedding_type}")

    # 如果没有指定embedding_type且模型已经加载，直接返回缓存的模型
    if _embedding_model is not None:
        # 打印模型缓存信息
        current_time = time.time()
        cache_duration = current_time - _model_load_time
        print(f"[使用缓存模型] 模型已缓存 {cache_duration:.1f} 秒")
        return _embedding_model

    # 首次加载默认模型
    print("[加载模型] 首次初始化默认向量模型...")
    start_time = time.time()

    from app.core.embedding import HuggingFaceEmbedding
    cfg = get_model_config_sync()

    # 打印模型配置信息
    print(f"[配置信息] 模型类型: {cfg.model_type}")
    print(f"[配置信息] 模型名称: {cfg.model_name}")
    print(f"[配置信息] 模型路径: {cfg.model_path if cfg.model_path else '(使用默认路径)'}")
    print(f"[配置信息] 运行设备: {cfg.device}")

    _embedding_model = HuggingFaceEmbedding(cfg.model_name, cfg.device, cfg.model_path, cfg.cache_dir)

    # 预热模型
    _ = _embedding_model.generate("Hello world")

    _model_load_time = time.time()
    load_duration = _model_load_time - start_time
    print(f"[加载模型] 模型加载完成，耗时 {load_duration:.2f} 秒")

    return _embedding_model

async def get_vector_db_sync(database: str = None) -> VectorDatabase:
    global _vector_db

    # 如果数据库已经初始化且没有指定数据库名称，直接返回
    if _vector_db is not None and database is None:
        print("[使用缓存] 使用已初始化的向量数据库")
        return _vector_db

    # 首次初始化数据库
    print("[初始化] 首次初始化向量数据库...")
    start_time = time.time()

    cfg = get_vectordb_config_sync()

    # 打印数据库配置信息（不显示敏感信息）
    print(f"[配置信息] 数据库类型: {cfg.db_type}")
    # milvus_client连接方式参数
    print(f"[配置信息] 数据库URI: {cfg.uri if cfg.uri else '(未设置)'}")
    print(f"[配置信息] 数据库Token: {'*****' if cfg.token else '(未设置)'}")
    try:
        if cfg.db_type == 'milvus':
            from app.core.vectordb import MilvusVectorDB
            # 创建MilvusVectorDB实例
            print(f"[连接] 使用URI方式连接Milvus: {cfg.uri}")
            if database:
                print(f"[连接] 使用数据库: {database}")
            else:
                print("[连接] 使用默认数据库")
            _vector_db = MilvusVectorDB(
                uri=cfg.uri,
                token=cfg.token if cfg.token and len(cfg.token.strip()) > 0 else None,
                database=database
            )

            # 连接到Milvus数据库
            try:
                await _vector_db.connect()
                print(f"[成功] 成功连接到Milvus数据库")
            except Exception as conn_error:
                print(f"[错误] Milvus数据库连接失败: {conn_error}")
                raise Exception(f"Milvus连接失败: {conn_error}")
        else:
            raise ValueError(f"不支持的向量数据库类型: {cfg.db_type}")
    except Exception as e:
        print(f"[错误] 向量数据库初始化失败: {e}")
        raise

    init_duration = time.time() - start_time
    print(f"[初始化] 向量数据库初始化完成，耗时 {init_duration:.2f} 秒")

    return _vector_db

def init_components(
    model_config: ModelConfig = Depends(get_model_config),
    db_config: VectorDBConfig = Depends(get_vectordb_config)
) -> Dict[str, Any]:
    return {
        "model_config": model_config,
        "db_config": db_config
    }

class AsyncTaskQueue:
    def __init__(self):
        self.client = httpx.AsyncClient()

    async def enqueue(self, task):
        # 实际生产环境应使用Redis/Celery等消息队列
        return await task

task_queue = AsyncTaskQueue()