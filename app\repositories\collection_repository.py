"""
集合管理Repository

负责：
- 集合创建和删除
- 集合配置管理
- 混合检索集合创建
- 集合状态查询
"""

from typing import Optional, Dict, Any, List
from app.core.vectordb import VectorDatabase


class CollectionRepository:
    """集合管理Repository"""
    
    def __init__(self, db: VectorDatabase):
        """
        初始化集合管理Repository
        
        Args:
            db: 数据库连接实例
        """
        self.db = db
    
    async def create_collection(
        self,
        collection_name: str,
        vector_dimension: int,
        auto_id: bool = True,
        enable_dynamic_field: bool = True
    ) -> None:
        """
        创建普通向量集合
        
        Args:
            collection_name: 集合名称
            vector_dimension: 向量维度
            auto_id: 是否自动生成ID
            enable_dynamic_field: 是否启用动态字段
            
        Raises:
            Exception: 创建失败
        """
        print(f"[集合] 创建普通向量集合: {collection_name}")
        print(f"[集合] 配置 - 向量维度: {vector_dimension}, 自动ID: {auto_id}, 动态字段: {enable_dynamic_field}")
        
        try:
            # 检查集合是否存在
            if await self.collection_exists(collection_name):
                print(f"[集合] 集合 '{collection_name}' 已存在，跳过创建")
                return
            
            # 创建集合的具体逻辑根据数据库类型而定
            if hasattr(self.db, 'create_collection'):
                # 使用数据库提供的create_collection方法
                create_method = getattr(self.db, 'create_collection')
                
                # 检查方法签名以决定如何调用
                try:
                    # 尝试调用带参数的版本
                    await create_method(
                        collection_name, 
                        vector_dimension,
                        auto_id=auto_id,
                        enable_dynamic_field=enable_dynamic_field
                    )
                except TypeError:
                    # 如果参数不匹配，尝试简化版本
                    await create_method(collection_name, vector_dimension)
                
                print(f"[成功] 普通向量集合 '{collection_name}' 创建成功")
            else:
                raise Exception("数据库不支持集合创建操作")
                
        except Exception as e:
            print(f"[错误] 创建集合 '{collection_name}' 失败: {e}")
            raise Exception(f"创建集合失败: {str(e)}")
    
    async def create_hybrid_collection(
        self,
        collection_name: str,
        dense_vector_dim: int,
        auto_id: bool = True,
        enable_dynamic_field: bool = True
    ) -> None:
        """
        创建混合检索集合（支持密集向量+稀疏向量）
        
        Args:
            collection_name: 集合名称
            dense_vector_dim: 密集向量维度
            auto_id: 是否自动生成ID
            enable_dynamic_field: 是否启用动态字段
            
        Raises:
            Exception: 创建失败
        """
        print(f"[集合] 创建混合检索集合: {collection_name}")
        print(f"[集合] 配置 - 密集向量维度: {dense_vector_dim}, 自动ID: {auto_id}, 动态字段: {enable_dynamic_field}")
        
        try:
            # 检查集合是否存在
            if await self.collection_exists(collection_name):
                print(f"[集合] 混合检索集合 '{collection_name}' 已存在，跳过创建")
                return
            
            # 创建混合检索集合
            if hasattr(self.db, 'create_hybrid_collection'):
                create_hybrid_method = getattr(self.db, 'create_hybrid_collection')
                await create_hybrid_method(
                    collection_name=collection_name,
                    dense_vector_dim=dense_vector_dim,
                    auto_id=auto_id,
                    enable_dynamic_field=enable_dynamic_field
                )
                print(f"[成功] 混合检索集合 '{collection_name}' 创建成功")
            else:
                raise Exception("数据库不支持混合检索集合创建")
                
        except Exception as e:
            print(f"[错误] 创建混合检索集合 '{collection_name}' 失败: {e}")
            raise Exception(f"创建混合检索集合失败: {str(e)}")
    
    async def delete_collection(self, collection_name: str) -> None:
        """
        删除集合
        
        Args:
            collection_name: 集合名称
            
        Raises:
            Exception: 删除失败
        """
        print(f"[集合] 删除集合: {collection_name}")
        
        try:
            # 检查集合是否存在
            if not await self.collection_exists(collection_name):
                print(f"[集合] 集合 '{collection_name}' 不存在，跳过删除")
                return
            
            if hasattr(self.db, 'drop_collection'):
                drop_method = getattr(self.db, 'drop_collection')
                await drop_method(collection_name)
                print(f"[成功] 集合 '{collection_name}' 删除成功")
            else:
                raise Exception("数据库不支持集合删除操作")
                
        except Exception as e:
            print(f"[错误] 删除集合 '{collection_name}' 失败: {e}")
            raise Exception(f"删除集合失败: {str(e)}")
    
    async def collection_exists(self, collection_name: str) -> bool:
        """
        检查集合是否存在
        
        Args:
            collection_name: 集合名称
            
        Returns:
            bool: 集合是否存在
        """
        try:
            if hasattr(self.db, 'has_collection'):
                has_collection_method = getattr(self.db, 'has_collection')
                return await has_collection_method(collection_name)
            elif hasattr(self.db, 'collection_exists'):
                exists_method = getattr(self.db, 'collection_exists')
                return await exists_method(collection_name)
            else:
                # 如果没有专门的方法，尝试获取集合信息
                try:
                    await self.get_collection_info(collection_name)
                    return True
                except:
                    return False
        except Exception as e:
            print(f"[警告] 检查集合 '{collection_name}' 是否存在时出错: {e}")
            return False
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """
        获取集合信息
        
        Args:
            collection_name: 集合名称
            
        Returns:
            Dict[str, Any]: 集合信息
            
        Raises:
            Exception: 获取失败
        """
        try:
            if hasattr(self.db, 'describe_collection'):
                describe_method = getattr(self.db, 'describe_collection')
                return await describe_method(collection_name)
            elif hasattr(self.db, 'get_collection_info'):
                info_method = getattr(self.db, 'get_collection_info')
                return await info_method(collection_name)
            else:
                raise Exception("数据库不支持获取集合信息操作")
        except Exception as e:
            print(f"[错误] 获取集合 '{collection_name}' 信息失败: {e}")
            raise Exception(f"获取集合信息失败: {str(e)}")
    
    async def list_collections(self) -> List[str]:
        """
        列出所有集合
        
        Returns:
            List[str]: 集合名称列表
            
        Raises:
            Exception: 获取失败
        """
        try:
            if hasattr(self.db, 'list_collections'):
                list_method = getattr(self.db, 'list_collections')
                return await list_method()
            elif hasattr(self.db, 'get_collections'):
                get_method = getattr(self.db, 'get_collections')
                return await get_method()
            else:
                raise Exception("数据库不支持列出集合操作")
        except Exception as e:
            print(f"[错误] 列出集合失败: {e}")
            raise Exception(f"列出集合失败: {str(e)}")
    
    async def flush_collection(self, collection_name: str) -> None:
        """
        刷新集合数据
        
        Args:
            collection_name: 集合名称
            
        Raises:
            Exception: 刷新失败
        """
        try:
            if hasattr(self.db, 'flush_collection'):
                flush_method = getattr(self.db, 'flush_collection')
                await flush_method(collection_name)
                print(f"[成功] 集合 '{collection_name}' 刷新成功")
            else:
                print(f"[警告] 数据库不支持集合刷新操作")
        except Exception as e:
            print(f"[错误] 刷新集合 '{collection_name}' 失败: {e}")
            raise Exception(f"刷新集合失败: {str(e)}")
    
    async def load_collection(self, collection_name: str) -> None:
        """
        加载集合到内存
        
        Args:
            collection_name: 集合名称
            
        Raises:
            Exception: 加载失败
        """
        try:
            if hasattr(self.db, 'load_collection'):
                load_method = getattr(self.db, 'load_collection')
                await load_method(collection_name)
                print(f"[成功] 集合 '{collection_name}' 加载成功")
            else:
                print(f"[警告] 数据库不支持集合加载操作")
        except Exception as e:
            print(f"[错误] 加载集合 '{collection_name}' 失败: {e}")
            raise Exception(f"加载集合失败: {str(e)}")
    
    async def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Args:
            collection_name: 集合名称
            
        Returns:
            Dict[str, Any]: 统计信息
            
        Raises:
            Exception: 获取失败
        """
        try:
            stats = {}
            
            # 尝试获取记录数
            if hasattr(self.db, 'get_collection_stats'):
                stats_method = getattr(self.db, 'get_collection_stats')
                db_stats = await stats_method(collection_name)
                stats.update(db_stats)
            
            # 尝试获取向量数量
            if hasattr(self.db, 'count_vectors'):
                count_method = getattr(self.db, 'count_vectors')
                vector_count = await count_method(collection_name)
                stats['vector_count'] = vector_count
            
            return stats
        except Exception as e:
            print(f"[错误] 获取集合 '{collection_name}' 统计信息失败: {e}")
            raise Exception(f"获取集合统计信息失败: {str(e)}") 