#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版Milvus向量数据库迁移工具 - 使用迭代器解决16384限制
专门解决 "invalid max query result window, (offset+limit) should be in range [1, 16384]" 错误
"""

import os
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB
from migration_config import SOURCE_CONFIG, TARGET_CONFIG, MIGRATION_CONFIG, LOGGING_CONFIG

# 加载环境变量
load_dotenv()

class IteratorMilvusMigrator:
    def __init__(self):
        # 源数据库配置（优先使用配置文件，其次使用环境变量）
        self.source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
        self.source_token = SOURCE_CONFIG.get("token") or os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
        
        # 目标数据库配置（从配置文件读取）
        self.target_uri = TARGET_CONFIG.get("uri", "")
        self.target_token = TARGET_CONFIG.get("token", "")
        self.target_database = TARGET_CONFIG.get("database", "")
        
        # 迁移配置
        self.batch_size = MIGRATION_CONFIG.get("batch_size", 1000)
        self.verify_after_migration = MIGRATION_CONFIG.get("verify_after_migration", True)
        
        # 数据库连接
        self.source_db = None
        self.target_db = None
        
        # PyMilvus连接别名
        self.source_alias = None
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        log_level = getattr(logging, LOGGING_CONFIG.get("log_level", "INFO"))
        log_file = LOGGING_CONFIG.get("log_file", "migration_iterator.log")
        
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        self.logger = logging.getLogger('IteratorMilvusMigrator')
        self.logger.setLevel(log_level)
        
        # 清除现有处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 控制台处理器
        if LOGGING_CONFIG.get("console_output", True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
    
    async def connect_databases(self) -> bool:
        """连接源数据库和目标数据库"""
        self.logger.info("开始连接数据库...")
        
        try:
            # 连接源数据库
            self.logger.info(f"连接源数据库: {self.source_uri}")
            self.source_db = MilvusVectorDB(
                uri=self.source_uri, 
                token=self.source_token, 
                database=self.source_database
            )
            await self.source_db.connect()
            self.logger.info("源数据库连接成功")
            
            # 设置PyMilvus连接用于迭代器
            await self.setup_pymilvus_connection()
            
            # 检查目标数据库配置
            if not self.target_uri:
                self.logger.error("目标数据库URI未配置")
                return False
                
            # 连接目标数据库
            self.logger.info(f"连接目标数据库: {self.target_uri}")
            self.target_db = MilvusVectorDB(
                uri=self.target_uri, 
                token=self.target_token, 
                database=self.target_database
            )
            await self.target_db.connect()
            self.logger.info("目标数据库连接成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    async def setup_pymilvus_connection(self):
        """设置PyMilvus连接，用于迭代器"""
        try:
            from pymilvus import connections
            
            # 创建连接别名
            self.source_alias = f"migration_source_{id(self)}"
            
            # 检查连接是否已存在
            if self.source_alias in connections.list_connections():
                self.logger.debug(f"连接 {self.source_alias} 已存在")
                return
            
            # 创建新连接
            connect_params = {
                "alias": self.source_alias,
                "uri": self.source_uri
            }
            
            if self.source_token:
                connect_params["token"] = self.source_token
            
            if self.source_database:
                connect_params["db_name"] = self.source_database
            
            connections.connect(**connect_params)
            
            self.logger.info(f"PyMilvus连接 {self.source_alias} 创建成功")
            
        except Exception as e:
            self.logger.error(f"设置PyMilvus连接失败: {e}")
            raise
    
    async def get_collections_to_migrate(self) -> List[str]:
        """获取需要迁移的集合列表"""
        await self.source_db._refresh_collections()
        all_collections = list(self.source_db.collections_cache)
        return all_collections
    
    def get_vector_dimension_from_user(self, collection_name: str) -> Optional[int]:
        """让用户手动输入向量维度"""
        print(f"\n⚠️ 需要手动输入集合 '{collection_name}' 的向量维度")
        print("常见向量维度:")
        print("  - 384  (sentence-transformers/all-MiniLM-L6-v2)")
        print("  - 768  (BERT base)")
        print("  - 1536 (OpenAI text-embedding-ada-002)")
        print("  - 3072 (OpenAI text-embedding-3-large)")
        print("  - 12288 (某些大型模型)")
        
        while True:
            try:
                dim_input = input(f"请输入 '{collection_name}' 的向量维度: ").strip()
                if dim_input:
                    dim = int(dim_input)
                    if dim > 0:
                        self.logger.info(f"用户输入向量维度: {dim}")
                        return dim
                    else:
                        print("❌ 向量维度必须是正整数")
                else:
                    print("❌ 请输入向量维度")
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                self.logger.error("用户取消输入")
                return None
    
    async def get_vector_dimension(self, collection_name: str) -> Optional[int]:
        """获取集合的向量维度"""
        try:
            # 方法1: 通过查询少量数据获取向量维度
            try:
                self.logger.info(f"尝试通过查询数据获取 {collection_name} 的向量维度")
                query_results = self.source_db.client.query(
                    collection_name=collection_name,
                    filter="",
                    output_fields=["vector"],
                    limit=1
                )
                
                if query_results and len(query_results) > 0:
                    vector = query_results[0].get('vector')
                    if vector and isinstance(vector, list):
                        dim = len(vector)
                        self.logger.info(f"通过查询数据获取到向量维度: {dim}")
                        return dim
            except Exception as e:
                self.logger.warning(f"通过查询数据获取向量维度失败: {e}")
            
            # 方法2: 让用户手动输入
            return self.get_vector_dimension_from_user(collection_name)
                    
        except Exception as e:
            self.logger.error(f"获取向量维度时发生错误: {e}")
            return None
    
    async def migrate_collection_with_iterator(self, collection_name: str) -> bool:
        """使用迭代器迁移单个集合，避免16384限制"""
        self.logger.info(f"开始使用迭代器迁移集合: {collection_name}")
        
        try:
            # 1. 获取集合统计信息
            try:
                stats = self.source_db.client.get_collection_stats(collection_name)
                total_count = stats.get('row_count', 0) if stats else 0

                # 确保 total_count 是一个有效的整数
                if total_count is None:
                    total_count = 0
                else:
                    total_count = int(total_count)

                self.logger.info(f"从统计信息获取到记录数: {total_count}")

            except Exception as e:
                self.logger.warning(f"无法获取集合统计信息: {e}")
                total_count = 0

            if total_count == 0:
                self.logger.warning(f"集合 {collection_name} 为空或无法获取统计信息，继续尝试迁移")
                # 不直接返回，而是继续尝试迁移，让迭代器自己判断是否有数据
            else:
                self.logger.info(f"集合 {collection_name} 包含 {total_count} 条记录")
            
            # 2. 获取向量维度
            vector_dim = await self.get_vector_dimension(collection_name)
            if vector_dim is None:
                self.logger.error(f"无法获取集合 {collection_name} 的向量维度")
                return False
            
            # 3. 创建目标集合
            self.logger.info(f"创建目标集合 {collection_name}，维度: {vector_dim}")
            await self.target_db.create_collection(collection_name, vector_dim)
            
            # 4. 使用迭代器迁移数据
            self.logger.info("使用PyMilvus迭代器进行数据迁移")
            
            from pymilvus import Collection
            
            # 创建Collection对象
            if not self.source_alias:
                self.logger.error("PyMilvus连接别名未设置")
                return False

            self.logger.debug(f"使用连接别名: {self.source_alias}")
            collection = Collection(collection_name, using=self.source_alias)
            
            # 确保集合已加载
            try:
                collection.load()
                self.logger.debug(f"集合 {collection_name} 已加载")
            except Exception as e:
                self.logger.warning(f"加载集合失败，可能已经加载: {e}")
            
            # 创建查询迭代器
            # 注意：不能使用 limit=None，需要使用一个足够大的数字
            # 使用一个很大的数字来确保获取所有数据
            max_limit = 1000000  # 100万，应该足够大了

            iterator = collection.query_iterator(
                batch_size=self.batch_size,
                limit=max_limit,  # 使用大数字而不是None
                expr="",  # 无过滤条件
                output_fields=["*"]  # 获取所有字段
            )
            
            migrated_count = 0
            batch_count = 0
            
            while True:
                try:
                    # 获取下一批数据
                    self.logger.debug(f"调用迭代器获取下一批数据...")
                    batch_data = iterator.next()

                    if not batch_data or len(batch_data) == 0:
                        self.logger.info("迭代器已完成，所有数据已处理")
                        break

                    batch_count += 1
                    self.logger.info(f"处理批次 {batch_count}: {len(batch_data)} 条记录")

                    # 插入数据
                    self.logger.debug(f"开始插入批次 {batch_count} 的数据...")
                    success = await self.insert_batch_data(collection_name, batch_data)

                    if success:
                        migrated_count += len(batch_data)

                        # 安全计算进度百分比
                        try:
                            if total_count and total_count > 0:
                                progress_percent = (migrated_count / total_count) * 100
                                self.logger.info(f"已迁移: {migrated_count}/{total_count} ({progress_percent:.1f}%)")
                            else:
                                self.logger.info(f"已迁移: {migrated_count} 条记录")
                        except Exception as e:
                            self.logger.warning(f"计算进度时出错: {e}")
                            self.logger.info(f"已迁移: {migrated_count} 条记录")
                    else:
                        self.logger.error(f"批次 {batch_count} 迁移失败")
                        iterator.close()
                        return False

                except Exception as e:
                    import traceback
                    self.logger.error(f"迭代器处理批次失败: {e}")
                    self.logger.error(f"错误详情: {traceback.format_exc()}")
                    iterator.close()
                    return False
            
            # 关闭迭代器
            iterator.close()
            self.logger.info("迭代器已关闭")
            
            self.logger.info(f"集合 {collection_name} 迁移完成，总计: {migrated_count} 条记录")
            
            # 验证迁移结果
            if self.verify_after_migration:
                verification_result = await self.verify_migration(collection_name)
                if not verification_result:
                    # 如果自动验证失败，询问用户是否手动确认
                    print(f"\n⚠️ 集合 '{collection_name}' 自动验证失败")
                    print("可能原因: Milvus统计更新延迟、索引构建中")
                    print("请手动检查目标数据库中的数据是否正确")

                    manual_confirm = input("数据是否迁移正确? (y/N): ").strip().lower()
                    if manual_confirm == 'y':
                        self.logger.info(f"用户手动确认集合 {collection_name} 迁移成功")
                        return True
                    else:
                        self.logger.error(f"用户确认集合 {collection_name} 迁移失败")
                        return False
                else:
                    return True

            return True

        except Exception as e:
            self.logger.error(f"迁移集合 {collection_name} 失败: {e}")
            return False

    async def insert_batch_data(self, collection_name: str, data: List[Dict[str, Any]]) -> bool:
        """插入批量数据"""
        try:
            # 数据格式转换
            records = []
            dynamic_fields_found = set()

            for item in data:
                # 创建新的记录副本
                record = {}

                for key, value in item.items():
                    if key == 'id':
                        # 跳过自动生成的id字段
                        continue
                    elif key == 'metadata':
                        # 确保metadata是字典格式
                        if isinstance(value, str):
                            try:
                                record['metadata'] = json.loads(value)
                                self.logger.debug("成功解析字符串格式的metadata")
                            except Exception as e:
                                self.logger.warning(f"无法解析metadata字符串: {e}")
                                record['metadata'] = {}
                        else:
                            record['metadata'] = value if isinstance(value, dict) else {}
                    else:
                        # 保留所有其他字段（包括动态字段）
                        record[key] = value

                        # 记录动态字段
                        if key not in ['vector', 'content', 'collection_name', 'metadata']:
                            dynamic_fields_found.add(key)

                records.append(record)

            # 输出动态字段信息
            if dynamic_fields_found:
                self.logger.info(f"发现动态字段: {list(dynamic_fields_found)}")

            await self.target_db.insert_vectors(collection_name, records)
            return True

        except Exception as e:
            self.logger.error(f"插入批量数据失败: {e}")
            return False

    async def verify_migration(self, collection_name: str) -> bool:
        """验证迁移结果"""
        try:
            # 获取源数据库统计
            source_stats = self.source_db.client.get_collection_stats(collection_name)
            source_count = source_stats.get('row_count', 0)

            self.logger.info(f"开始验证集合 {collection_name}，源数据库记录数: {source_count}")

            # 等待目标数据库统计更新，最多重试5次
            max_retries = 5
            wait_seconds = 2

            for attempt in range(max_retries):
                try:
                    # 刷新目标集合统计
                    self.logger.info(f"第 {attempt + 1} 次验证，等待 {wait_seconds} 秒...")
                    await asyncio.sleep(wait_seconds)

                    # 尝试刷新集合加载状态
                    try:
                        self.target_db.client.load_collection(collection_name)
                        await asyncio.sleep(1)  # 等待加载完成
                    except Exception as e:
                        self.logger.debug(f"刷新集合加载状态时出错: {e}")

                    # 获取目标数据库统计
                    target_stats = self.target_db.client.get_collection_stats(collection_name)
                    target_count = target_stats.get('row_count', 0)

                    self.logger.info(f"验证尝试 {attempt + 1}: 源:{source_count} 目标:{target_count}")

                    if source_count == target_count:
                        self.logger.info(f"✅ 验证成功: {collection_name} ({source_count} 条记录)")
                        return True
                    elif target_count > source_count:
                        self.logger.warning(f"⚠️ 目标记录数超过源记录数: {collection_name} 源:{source_count} 目标:{target_count}")
                        # 如果目标记录数更多，可能是正常的（比如有重复数据处理）
                        return True
                    else:
                        # 如果还没达到预期，继续等待
                        diff = source_count - target_count
                        self.logger.info(f"还差 {diff} 条记录，继续等待...")
                        wait_seconds = min(wait_seconds * 1.5, 10)  # 逐渐增加等待时间

                except Exception as e:
                    self.logger.warning(f"验证尝试 {attempt + 1} 失败: {e}")
                    continue

            # 最终验证
            try:
                target_stats = self.target_db.client.get_collection_stats(collection_name)
                target_count = target_stats.get('row_count', 0)

                if source_count == target_count:
                    self.logger.info(f"✅ 最终验证成功: {collection_name} ({source_count} 条记录)")
                    return True
                else:
                    diff = abs(source_count - target_count)
                    diff_percent = (diff / source_count) * 100 if source_count > 0 else 0

                    if diff_percent <= 1.0:  # 允许1%的误差
                        self.logger.warning(f"⚠️ 验证通过(允许误差): {collection_name} 源:{source_count} 目标:{target_count} 误差:{diff_percent:.2f}%")
                        return True
                    else:
                        self.logger.error(f"❌ 验证失败: {collection_name} 源:{source_count} 目标:{target_count} 误差:{diff_percent:.2f}%")

                        # 提供详细的诊断信息
                        self.logger.info("🔍 诊断信息:")
                        self.logger.info(f"  - 数据差异: {diff} 条记录")
                        self.logger.info(f"  - 可能原因: Milvus统计延迟、索引构建中、数据重复处理")
                        self.logger.info(f"  - 建议: 等待几分钟后手动检查目标数据库")

                        return False

            except Exception as e:
                self.logger.error(f"最终验证失败: {e}")
                return False

        except Exception as e:
            self.logger.error(f"验证迁移结果失败: {e}")
            return False

    async def run_migration(self, selected_collections: List[str] = None):
        """运行迁移任务"""
        self.logger.info("开始迭代器版迁移任务")

        # 连接数据库
        if not await self.connect_databases():
            self.logger.error("数据库连接失败，退出迁移")
            return

        # 获取要迁移的集合
        if selected_collections:
            collections = selected_collections
        else:
            collections = await self.get_collections_to_migrate()

        if not collections:
            self.logger.info("没有需要迁移的集合")
            return

        self.logger.info(f"将迁移 {len(collections)} 个集合: {collections}")

        # 开始迁移
        success_count = 0
        failed_collections = []

        start_time = time.time()

        for i, collection in enumerate(collections, 1):
            self.logger.info(f"[{i}/{len(collections)}] 处理集合: {collection}")

            success = await self.migrate_collection_with_iterator(collection)

            if success:
                success_count += 1
                self.logger.info(f"✅ 集合 {collection} 迁移成功")
            else:
                failed_collections.append(collection)
                self.logger.error(f"❌ 集合 {collection} 迁移失败")

        # 迁移总结
        end_time = time.time()
        duration = end_time - start_time

        self.logger.info("=" * 60)
        self.logger.info("迁移任务完成")
        self.logger.info(f"总耗时: {duration:.2f} 秒")
        self.logger.info(f"成功迁移: {success_count} 个集合")
        self.logger.info(f"失败集合: {len(failed_collections)} 个")

        if failed_collections:
            self.logger.error(f"失败的集合: {failed_collections}")
        else:
            self.logger.info("🎉 所有集合迁移成功！")

async def main():
    """主函数"""
    print("🔧 迭代器版Milvus向量数据库迁移工具")
    print("专门解决16384查询限制问题")
    print("=" * 60)

    migrator = IteratorMilvusMigrator()

    print("\n选择操作:")
    print("1. 迁移所有集合")
    print("2. 迁移指定集合")

    choice = input("请选择 (1-2): ").strip()

    if choice == "1":
        await migrator.run_migration()
    elif choice == "2":
        # 获取集合列表供选择
        if await migrator.connect_databases():
            await migrator.source_db._refresh_collections()
            collections = list(migrator.source_db.collections_cache)

            print("\n可用集合:")
            for i, col in enumerate(collections, 1):
                print(f"  {i}. {col}")

            selection = input("请输入集合序号 (用逗号分隔): ").strip()
            try:
                indices = [int(x.strip()) - 1 for x in selection.split(',')]
                selected = [collections[i] for i in indices if 0 <= i < len(collections)]
                await migrator.run_migration(selected)
            except:
                print("输入格式错误")
    else:
        print("无效选择")

if __name__ == "__main__":
    asyncio.run(main())
