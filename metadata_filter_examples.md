# Milvus JSON Metadata 筛选使用指南

## 概述

本文档说明如何在search接口中对JSON类型的metadata字段进行筛选。系统支持多种筛选方式，可以灵活地根据metadata中的不同字段进行数据筛选。

## 支持的筛选方式

### 1. 直接使用 filter_expr 参数

最直接的方式是在请求中使用 `filter_expr` 参数，直接编写Milvus筛选表达式：

```json
{
    "text": "搜索内容",
    "top_k": 5,
    "collection": "documents",
    "filter_expr": "metadata[\"category\"] == \"electronics\""
}
```

#### 支持的表达式类型：

**基本比较：**
```json
{
    "filter_expr": "metadata[\"category\"] == \"electronics\""
}
```

**嵌套字段筛选：**
```json
{
    "filter_expr": "metadata[\"product_info\"][\"category\"] == \"electronics\""
}
```

**数值比较：**
```json
{
    "filter_expr": "metadata[\"price\"] < 100"
}
```

**布尔值筛选：**
```json
{
    "filter_expr": "metadata[\"in_stock\"] == true"
}
```

**组合条件：**
```json
{
    "filter_expr": "metadata[\"category\"] == \"electronics\" and metadata[\"price\"] < 100"
}
```

### 2. 使用 metadata_filters 参数（推荐）

新增的 `metadata_filters` 参数提供了更简洁的方式来筛选metadata字段：

```json
{
    "text": "搜索内容",
    "top_k": 5,
    "collection": "documents",
    "metadata_filters": {
        "category": "electronics",
        "price": {"$lt": 100},
        "in_stock": true
    }
}
```

#### 支持的操作符：

**等于比较：**
```json
{
    "metadata_filters": {
        "category": "electronics"
    }
}
```

**数值比较：**
```json
{
    "metadata_filters": {
        "price": {"$lt": 100},          // 小于
        "rating": {"$gte": 4.0},        // 大于等于
        "discount": {"$gt": 0.1},       // 大于
        "stock": {"$lte": 50}           // 小于等于
    }
}
```

**不等于：**
```json
{
    "metadata_filters": {
        "status": {"$ne": "discontinued"}
    }
}
```

**包含检查：**
```json
{
    "metadata_filters": {
        "tags": {"$in": ["sale", "new", "featured"]}
    }
}
```

**组合条件：**
```json
{
    "metadata_filters": {
        "category": "electronics",
        "price": {"$lt": 100},
        "in_stock": true,
        "rating": {"$gte": 4.0}
    }
}
```

### 3. 同时使用多种筛选方式

可以同时使用 `filter_expr` 和 `metadata_filters`，系统会自动合并这些条件：

```json
{
    "text": "搜索内容",
    "filter_expr": "brand == \"Apple\"",
    "metadata_filters": {
        "category": "electronics",
        "price": {"$lt": 1000}
    }
}
```

最终的筛选表达式将是：
```
brand == "Apple" and metadata["category"] == "electronics" and metadata["price"] < 1000
```

## 实际使用示例

### 示例1：搜索电子产品

```json
POST /api/v1/search
{
    "text": "智能手机",
    "top_k": 10,
    "collection": "products",
    "metadata_filters": {
        "category": "electronics",
        "subcategory": "smartphone",
        "price": {"$lt": 5000},
        "in_stock": true
    }
}
```

### 示例2：搜索特定品牌的产品

```json
POST /api/v1/search
{
    "text": "笔记本电脑",
    "top_k": 5,
    "collection": "products",
    "filter_expr": "metadata[\"product_info\"][\"brand\"] == \"Apple\"",
    "metadata_filters": {
        "price": {"$gte": 8000, "$lte": 20000}
    }
}
```

### 示例3：搜索促销商品

```json
POST /api/v1/search
{
    "text": "优惠商品",
    "top_k": 20,
    "collection": "products",
    "metadata_filters": {
        "on_sale": true,
        "discount": {"$gt": 0.2},
        "tags": {"$in": ["clearance", "limited_time"]}
    }
}
```

## 多数据库搜索

多数据库搜索接口 `/api/v1/multi_search` 也支持相同的metadata筛选功能：

```json
POST /api/v1/multi_search
{
    "text": "搜索内容",
    "top_k": 5,
    "total_results": 10,
    "databases": ["db1", "db2"],
    "metadata_filters": {
        "category": "electronics",
        "price": {"$lt": 100}
    }
}
```

## 注意事项

1. **字段类型匹配**：确保筛选条件的数据类型与存储在metadata中的数据类型匹配
2. **嵌套字段**：对于嵌套的JSON字段，建议使用 `filter_expr` 方式
3. **性能考虑**：为了提高查询性能，建议为常用的metadata字段创建索引
4. **字符串处理**：字符串值会自动添加引号，无需手动添加
5. **布尔值**：布尔值会自动转换为小写（true/false）

## 错误处理

如果筛选表达式有语法错误或字段不存在，系统会返回相应的错误信息。建议在生产环境中先测试筛选表达式的正确性。
