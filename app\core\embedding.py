from abc import ABC, abstractmethod
from typing import List
import os
import time
from openai import AzureOpenAI, OpenAI

class EmbeddingModel(ABC):
    @abstractmethod
    def generate(self, text: str) -> List[float]:
        pass

    @abstractmethod
    async def agenerate(self, text: str) -> List[float]:
        pass

    @abstractmethod
    def get_dimension(self) -> int:
        """Return the dimension of the embedding vectors"""
        pass

class HuggingFaceEmbedding(EmbeddingModel):
    def __init__(self, model_name: str, device: str = 'cpu', model_path: str = None, cache_dir: str = None):
        self.model_name = model_name
        self.device = device
        self.model_path = model_path
        self.cache_dir = cache_dir
        self.dimension = 768  # Default dimension for most models

    def load_model(self):
        # 延迟加载模型
        from sentence_transformers import SentenceTransformer
        self.model = SentenceTransformer(self.model_path or self.model_name, device=self.device, cache_folder=self.cache_dir)
        # Update dimension based on the loaded model
        self.dimension = self.model.get_sentence_embedding_dimension()

    def generate(self, text: str) -> List[float]:
        if not hasattr(self, 'model'):
            self.load_model()
        return self.model.encode(text).tolist()

    async def agenerate(self, text: str) -> List[float]:
        return self.generate(text)

    def get_dimension(self) -> int:
        if not hasattr(self, 'model'):
            self.load_model()
        return self.dimension


class AzureOpenAIEmbedding(EmbeddingModel):
    """
    使用Azure OpenAI API生成文本嵌入向量

    支持的模型:
    - text-embedding-3-small (1536维)
    - text-embedding-3-large (3072维)
    - text-embedding-ada-002 (1536维，旧版)
    """

    def __init__(self,
                 api_key: str = None,
                 api_version: str = "2024-10-21",
                 azure_endpoint: str = None,
                 azure_deployment: str = None,
                 model_name: str = "text-embedding-3-small"):
        """
        初始化Azure OpenAI Embedding模型

        Args:
            api_key: Azure OpenAI API密钥，如果不提供则从环境变量AZURE_OPENAI_API_KEY获取
            api_version: API版本，默认为2024-10-21
            azure_endpoint: Azure OpenAI端点，如果不提供则从环境变量AZURE_OPENAI_ENDPOINT获取
            model_name: 模型名称，默认为text-embedding-3-small
        """
        self.api_key = api_key or os.environ.get("AZURE_OPENAI_API_KEY", "")
        if not self.api_key:
            raise ValueError("Azure OpenAI API密钥未提供，请设置AZURE_OPENAI_API_KEY环境变量或在初始化时提供api_key参数")

        self.azure_endpoint = azure_endpoint or os.environ.get("AZURE_OPENAI_ENDPOINT", "")
        if not self.azure_endpoint:
            raise ValueError("Azure OpenAI端点未提供，请设置AZURE_OPENAI_ENDPOINT环境变量或在初始化时提供azure_endpoint参数")

        self.azure_deployment = azure_deployment or os.environ.get("AZURE_OPENAI_DEPLOYMENT", "")
        if not self.azure_deployment:
            raise ValueError("Azure OpenAI Deployment未设置，请设置AZURE_OPENAI_DEPLOYMENT环境变量")

        self.api_version = api_version
        self.model_name = model_name

        # 根据模型名称设置维度
        if model_name == "text-embedding-3-large":
            self.dimension = 3072
        else:  # text-embedding-3-small 或 text-embedding-ada-002
            self.dimension = 1536

        # 创建Azure OpenAI客户端
        self.client = None

        print(f"初始化Azure OpenAI Embedding模型: {model_name}, 维度: {self.dimension}")
        print(f"Azure端点: {self.azure_endpoint}")
        print(f"API版本: {self.api_version}")

    def _get_client(self):
        """获取Azure OpenAI客户端"""
        if self.client is None:
            try:
                # 显式指定支持的参数，避免传入不支持的参数
                client_params = {
                    'api_key': self.api_key,
                    'api_version': self.api_version,
                    'azure_deployment': self.azure_deployment,
                    'azure_endpoint': self.azure_endpoint
                }

                # 过滤掉None值
                client_params = {k: v for k, v in client_params.items() if v is not None}

                print(f"[调试] 创建Azure OpenAI客户端，参数: {list(client_params.keys())}")
                print(f"[调试] 参数值: {client_params}")

                # 检查环境变量中是否有可能影响的配置
                import os
                proxy_vars = [k for k in os.environ.keys() if 'PROXY' in k.upper() or 'HTTP' in k.upper()]
                if proxy_vars:
                    print(f"[警告] 发现代理相关环境变量: {proxy_vars}")

                # 尝试导入并检查AzureOpenAI类的签名
                from openai import AzureOpenAI
                import inspect
                sig = inspect.signature(AzureOpenAI.__init__)
                print(f"[调试] AzureOpenAI.__init__ 支持的参数: {list(sig.parameters.keys())}")

                self.client = AzureOpenAI(**client_params)

                print(f"[成功] Azure OpenAI客户端创建成功")

            except Exception as e:
                print(f"[错误] 创建Azure OpenAI客户端失败: {str(e)}")
                print(f"[调试] 错误类型: {type(e).__name__}")
                print(f"[调试] 完整错误信息:")
                import traceback
                traceback.print_exc()
                raise
        return self.client

    def generate(self, text: str) -> List[float]:
        """
        同步生成文本嵌入向量

        Args:
            text: 要生成嵌入向量的文本

        Returns:
            嵌入向量列表
        """
        start_time = time.time()
        try:
            client = self._get_client()

            response = client.embeddings.create(
                input=text,
                model=self.model_name
            )

            vector = response.data[0].embedding

            # 打印请求信息
            elapsed = time.time() - start_time
            print(f"Azure OpenAI Embedding生成成功，模型: {self.model_name}, 耗时: {elapsed:.3f}秒")

            return vector

        except Exception as e:
            print(f"Azure OpenAI Embedding生成失败: {str(e)}")
            raise

    async def agenerate(self, text: str) -> List[float]:
        """
        异步生成文本嵌入向量（实际上是同步实现的包装）

        Args:
            text: 要生成嵌入向量的文本

        Returns:
            嵌入向量列表
        """
        # 由于Azure OpenAI SDK没有提供异步API，我们在这里使用同步API
        # 在实际的异步环境中，这可能会阻塞事件循环
        # 更好的方法是使用线程池执行器来运行同步代码
        return self.generate(text)

    def get_dimension(self) -> int:
        """
        返回嵌入向量的维度

        Returns:
            嵌入向量的维度
        """
        return self.dimension



class BgeM3Embedding(EmbeddingModel):
    """
    使用Bge-m3 API生成文本嵌入向量

    支持的模型:
    - BAAI/bge-m3 (1024维)
    """

    def __init__(self,
                 model_name: str = "embedding",
                 api_key: str = None,
                 base_url: str = "http://10.122.83.47:9103/v1"
):
        """
        初始化Bge-m3 Embedding模型

        Args:
            api_key: API密钥，如果不提供则从环境变量PRIVATE_API_KEY获取
            base_url: 私有化部署模型，如果不提供则从环境变量PRIVATE_BASE_URL获取
            model_name: 模型名称，默认为embedding
        """
        self.api_key = api_key or os.environ.get("PRIVATE_API_KEY", "")
        if not self.api_key:
            raise ValueError("Bge-m3 API密钥未提供，请设置PRIVATE_API_KEY环境变量或在初始化时提供api_key参数")

        self.base_url = base_url or os.environ.get("PRIVATE_BASE_URL", "")
        if not self.base_url:
            raise ValueError("Bge-m3 base_url未提供，请设置PRIVATE_BASE_URL环境变量或在初始化时提供base_url参数")

        self.model_name = model_name

        # 根据模型名称设置维度
        if model_name == "embedding":
            self.dimension = 512

        # 创建客户端
        self.client = None

        print(f"初始化Bge-m3 Embedding模型: {model_name}, 维度: {self.dimension}")
        print(f"Base_url: {self.base_url}")

    def _get_client(self):
        """获取客户端"""
        if self.client is None:
            try:
                # 显式指定支持的参数，避免传入不支持的参数
                client_params = {
                    'api_key': self.api_key,
                    'base_url': self.base_url
                }

                # 过滤掉None值
                client_params = {k: v for k, v in client_params.items() if v is not None}

                print(f"[调试] 创建Bge-m3 OpenAI客户端，参数: {list(client_params.keys())}")

                self.client = OpenAI(**client_params)

                print(f"[成功] Bge-m3 OpenAI客户端创建成功")

            except Exception as e:
                print(f"[错误] 创建Bge-m3 OpenAI客户端失败: {str(e)}")
                print(f"[调试] 错误类型: {type(e).__name__}")
                raise
        return self.client

    def generate(self, text: str) -> List[float]:
        """
        同步生成文本嵌入向量

        Args:
            text: 要生成嵌入向量的文本

        Returns:
            嵌入向量列表
        """
        start_time = time.time()
        try:
            client = self._get_client()

            response = client.embeddings.create(
                input=text,
                model=self.model_name
            )

            vector = response.data[0].embedding

            # 打印请求信息
            elapsed = time.time() - start_time
            print(f"Azure OpenAI Embedding生成成功，模型: {self.model_name}, 耗时: {elapsed:.3f}秒")

            return vector

        except Exception as e:
            print(f"Azure OpenAI Embedding生成失败: {str(e)}")
            raise

    async def agenerate(self, text: str) -> List[float]:
        """
        异步生成文本嵌入向量（实际上是同步实现的包装）

        Args:
            text: 要生成嵌入向量的文本

        Returns:
            嵌入向量列表
        """
        # 由于Azure OpenAI SDK没有提供异步API，我们在这里使用同步API
        # 在实际的异步环境中，这可能会阻塞事件循环
        # 更好的方法是使用线程池执行器来运行同步代码
        return self.generate(text)

    def get_dimension(self) -> int:
        """
        返回嵌入向量的维度

        Returns:
            嵌入向量的维度
        """
        return self.dimension


class Qwen3Embedding(EmbeddingModel):
    """
    使用阿里云百炼的text-embedding-v4 API生成文本嵌入向量

    支持的模型:
    - Bailian/text-embedding-v4 (2,048、1,536、1,024（默认）、768、512、256、128、64维)
    """

    def __init__(self,
                 model_name: str = "text-embedding-v4",
                 api_key: str = None,
                 base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
):
        """
        初始化Qwen3 Embedding模型

        Args:
            api_key: API密钥，如果不提供则从环境变量PRIVATE_API_KEY获取
            base_url: 私有化部署模型，如果不提供则从环境变量PRIVATE_BASE_URL获取
            model_name: 模型名称，默认为embedding
        """
        self.api_key = api_key or os.environ.get("DASHSCOPE_API_KEY", "")
        if not self.api_key:
            raise ValueError("Qwen3 Embedding API密钥未提供，请设置PRIVATE_API_KEY环境变量或在初始化时提供api_key参数")

        self.base_url = base_url
        if not self.base_url:
            raise ValueError("Qwen3 Embedding base_url未提供，请设置PRIVATE_BASE_URL环境变量或在初始化时提供base_url参数")

        self.model_name = model_name

        # 根据模型名称设置维度
        if model_name == "text-embedding-v4":
            self.dimension = 2048

        # 创建客户端
        self.client = None

        print(f"初始化Qwen3 Embedding模型: {model_name}, 维度: {self.dimension}")
        print(f"Base_url: {self.base_url}")

    def _get_client(self):
        """获取客户端"""
        if self.client is None:
            try:
                # 显式指定支持的参数，避免传入不支持的参数
                client_params = {
                    'api_key': self.api_key,
                    'base_url': self.base_url
                }

                # 过滤掉None值
                client_params = {k: v for k, v in client_params.items() if v is not None}

                print(f"[调试] 创建Qwen3 OpenAI客户端，参数: {list(client_params.keys())}")

                self.client = OpenAI(**client_params)

                print(f"[成功] Qwen3 OpenAI客户端创建成功")

            except Exception as e:
                print(f"[错误] 创建Qwen3 OpenAI客户端失败: {str(e)}")
                print(f"[调试] 错误类型: {type(e).__name__}")
                raise
        return self.client

    def generate(self, text: str) -> List[float]:
        """
        同步生成文本嵌入向量

        Args:
            text: 要生成嵌入向量的文本

        Returns:
            嵌入向量列表
        """
        start_time = time.time()
        try:
            client = self._get_client()

            response = client.embeddings.create(
                input=text,
                model=self.model_name,
                dimensions=self.dimension,
                encoding_format="float"
            )

            vector = response.data[0].embedding

            # 打印请求信息
            elapsed = time.time() - start_time
            print(f"Azure OpenAI Embedding生成成功，模型: {self.model_name}, 耗时: {elapsed:.3f}秒")

            return vector

        except Exception as e:
            print(f"Azure OpenAI Embedding生成失败: {str(e)}")
            raise

    async def agenerate(self, text: str) -> List[float]:
        """
        异步生成文本嵌入向量（实际上是同步实现的包装）

        Args:
            text: 要生成嵌入向量的文本

        Returns:
            嵌入向量列表
        """
        # 由于Azure OpenAI SDK没有提供异步API，我们在这里使用同步API
        # 在实际的异步环境中，这可能会阻塞事件循环
        # 更好的方法是使用线程池执行器来运行同步代码
        return self.generate(text)

    def get_dimension(self) -> int:
        """
        返回嵌入向量的维度

        Returns:
            嵌入向量的维度
        """
        return self.dimension