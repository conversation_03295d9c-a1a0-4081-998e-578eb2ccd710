"""
日志工具模块
提供统一的文件日志记录功能，支持异步写入、文件轮转、结构化输出
"""
import json
import time
import os
import traceback
import threading
import queue
import gzip
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, Optional, List
from dataclasses import dataclass, asdict
from fastapi import Request


@dataclass
class LogConfig:
    """日志配置"""
    # 基础配置
    log_root_dir: str = "logs"
    max_file_size_mb: int = 100
    retention_days: int = 30
    
    # 异步写入配置
    async_write: bool = True
    buffer_size: int = 100
    flush_interval: float = 1.0
    
    # 文件分片配置
    shard_count: int = 10
    
    # 日志级别
    levels: Optional[Dict[str, int]] = None
    
    def __post_init__(self):
        if self.levels is None:
            self.levels = {
                "DEBUG": 10,
                "INFO": 20,
                "WARN": 30,
                "ERROR": 40,
                "PERF": 25
            }


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: str
    level: str
    category: str
    message: str
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    operation: Optional[str] = None
    duration_ms: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        data = asdict(self)
        # 移除None值
        data = {k: v for k, v in data.items() if v is not None}
        return json.dumps(data, ensure_ascii=False, default=str)


class FileRotator:
    """文件轮转器"""
    
    def __init__(self, config: LogConfig):
        self.config = config
        self.max_size = config.max_file_size_mb * 1024 * 1024  # 转换为字节
    
    def should_rotate(self, file_path: str) -> bool:
        """判断是否需要轮转"""
        if not os.path.exists(file_path):
            return False
        return os.path.getsize(file_path) >= self.max_size
    
    def rotate_file(self, file_path: str) -> str:
        """轮转文件"""
        if not os.path.exists(file_path):
            return file_path
        
        # 生成带时间戳的新文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(file_path)[0]
        rotated_name = f"{base_name}_{timestamp}.log"
        
        # 移动当前文件
        shutil.move(file_path, rotated_name)
        
        # 压缩旧文件（异步）
        threading.Thread(target=self._compress_file, args=(rotated_name,), daemon=True).start()
        
        return file_path
    
    def _compress_file(self, file_path: str):
        """压缩文件"""
        try:
            compressed_path = f"{file_path}.gz"
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            os.remove(file_path)
        except Exception:
            pass  # 压缩失败不影响主流程
    
    def cleanup_old_files(self, log_dir: str):
        """清理过期文件"""
        try:
            cutoff_time = datetime.now() - timedelta(days=self.config.retention_days)
            cutoff_timestamp = cutoff_time.timestamp()
            
            for file_path in Path(log_dir).rglob("*.log*"):
                if file_path.stat().st_mtime < cutoff_timestamp:
                    file_path.unlink(missing_ok=True)
        except Exception:
            pass  # 清理失败不影响主流程


class AsyncLogWriter:
    """异步日志写入器"""
    
    def __init__(self, config: LogConfig):
        self.config = config
        self.log_queue = queue.Queue(maxsize=config.buffer_size * 2)
        self.running = True
        self.rotator = FileRotator(config)
        
        # 创建日志目录
        self._create_log_directories()
        
        # 启动写入线程
        self.writer_thread = threading.Thread(target=self._write_loop, daemon=True)
        self.writer_thread.start()
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def _create_log_directories(self):
        """创建日志目录结构"""
        base_dir = Path(self.config.log_root_dir)
        directories = [
            "requests/upload",
            "requests/search", 
            "requests/management",
            "performance",
            "errors",
            "debug"
        ]
        
        for dir_path in directories:
            (base_dir / dir_path).mkdir(parents=True, exist_ok=True)
    
    def write_log(self, entry: LogEntry):
        """写入日志条目"""
        if not self.running:
            return
        
        try:
            self.log_queue.put_nowait(entry)
        except queue.Full:
            # 队列满时，丢弃最旧的日志
            try:
                self.log_queue.get_nowait()
                self.log_queue.put_nowait(entry)
            except queue.Empty:
                pass
    
    def _write_loop(self):
        """写入循环"""
        buffer = []
        last_flush = time.time()
        
        while self.running:
            try:
                # 尝试获取日志条目
                try:
                    entry = self.log_queue.get(timeout=0.1)
                    buffer.append(entry)
                except queue.Empty:
                    pass
                
                # 检查是否需要刷新
                should_flush = (
                    len(buffer) >= self.config.buffer_size or
                    time.time() - last_flush >= self.config.flush_interval
                )
                
                if should_flush and buffer:
                    self._flush_buffer(buffer)
                    buffer.clear()
                    last_flush = time.time()
                    
            except Exception:
                continue
        
        # 程序退出时刷新剩余的日志
        if buffer:
            self._flush_buffer(buffer)
    
    def _flush_buffer(self, buffer: List[LogEntry]):
        """刷新缓冲区"""
        # 按类别分组
        grouped_logs = {}
        for entry in buffer:
            category = entry.category
            if category not in grouped_logs:
                grouped_logs[category] = []
            grouped_logs[category].append(entry)
        
        # 写入不同的文件
        for category, entries in grouped_logs.items():
            file_path = self._get_file_path(category)
            self._write_to_file(file_path, entries)
    
    def _get_file_path(self, category: str) -> str:
        """获取文件路径"""
        base_dir = Path(self.config.log_root_dir)
        date_str = datetime.now().strftime("%Y-%m-%d")
        
        # 根据类别确定子目录
        if category in ["upload", "search", "management"]:
            subdir = f"requests/{category}"
        elif category == "performance":
            subdir = "performance"
        elif category == "error":
            subdir = "errors"
        else:
            subdir = "debug"
        
        return str(base_dir / subdir / f"{category}_{date_str}.log")
    
    def _write_to_file(self, file_path: str, entries: List[LogEntry]):
        """写入文件"""
        try:
            # 检查是否需要轮转
            if self.rotator.should_rotate(file_path):
                self.rotator.rotate_file(file_path)
            
            # 写入日志
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'a', encoding='utf-8') as f:
                for entry in entries:
                    f.write(entry.to_json() + '\n')
        except Exception:
            pass  # 写入失败不影响主流程
    
    def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                # 每小时执行一次清理
                time.sleep(3600)
                self.rotator.cleanup_old_files(self.config.log_root_dir)
            except Exception:
                continue
    
    def close(self):
        """关闭写入器"""
        self.running = False
        if self.writer_thread.is_alive():
            self.writer_thread.join(timeout=5)


class LogManager:
    """日志管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, config: Optional[LogConfig] = None):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self, config: Optional[LogConfig] = None):
        if self._initialized:
            return
        
        self.config = config or LogConfig()
        self.writer = AsyncLogWriter(self.config) if self.config.async_write else None
        self._initialized = True
    
    def log(self, level: str, category: str, message: str, **metadata):
        """记录日志"""
        # 提取LogEntry的预定义字段
        entry_fields = {
            'request_id': metadata.pop('request_id', None),
            'user_id': metadata.pop('user_id', None),
            'operation': metadata.pop('operation', None),
            'duration_ms': metadata.pop('duration_ms', None)
        }
        
        # 剩余的metadata作为metadata字段
        remaining_metadata = metadata if metadata else None
        
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=level,
            category=category,
            message=message,
            metadata=remaining_metadata,
            **{k: v for k, v in entry_fields.items() if v is not None}
        )
        
        if self.writer:
            self.writer.write_log(entry)
        else:
            # 同步写入
            file_path = self._get_file_path_sync(category)
            self._write_sync(file_path, entry)
    
    def _get_file_path_sync(self, category: str) -> str:
        """同步模式获取文件路径"""
        base_dir = Path(self.config.log_root_dir)
        date_str = datetime.now().strftime("%Y-%m-%d")
        
        if category in ["upload", "search", "management"]:
            subdir = f"requests/{category}"
        elif category == "performance":
            subdir = "performance"
        elif category == "error":
            subdir = "errors"
        else:
            subdir = "debug"
        
        os.makedirs(base_dir / subdir, exist_ok=True)
        return str(base_dir / subdir / f"{category}_{date_str}.log")
    
    def _write_sync(self, file_path: str, entry: LogEntry):
        """同步写入"""
        try:
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write(entry.to_json() + '\n')
        except Exception:
            pass
    
    def close(self):
        """关闭日志管理器"""
        if self.writer:
            self.writer.close()


# 全局日志管理器实例
_log_manager = LogManager()


class TimeTracker:
    """时间追踪器"""
    
    def __init__(self, operation_name: str = "操作", category: str = "performance"):
        self.operation_name = operation_name
        self.category = category
        self.start_time = None
        self.checkpoints = {}
        self.last_checkpoint = None
    
    def start(self) -> None:
        """开始计时"""
        self.start_time = time.time()
        self.last_checkpoint = self.start_time
        _log_manager.log(
            "INFO", 
            self.category, 
            f"{self.operation_name}开始",
            operation=self.operation_name,
            action="start"
        )
    
    def checkpoint(self, checkpoint_name: str) -> float:
        """设置检查点"""
        current_time = time.time()
        if self.last_checkpoint:
            duration = current_time - self.last_checkpoint
        else:
            duration = current_time - (self.start_time or current_time)
        
        self.checkpoints[checkpoint_name] = duration
        self.last_checkpoint = current_time
        
        _log_manager.log(
            "INFO",
            self.category,
            f"{checkpoint_name}完成",
            operation=self.operation_name,
            checkpoint=checkpoint_name,
            duration_ms=duration * 1000,
            action="checkpoint"
        )
        return duration
    
    def finish(self) -> Dict[str, float]:
        """完成计时"""
        if not self.start_time:
            return {}
        
        total_time = time.time() - self.start_time
        self.checkpoints['total'] = total_time
        
        _log_manager.log(
            "INFO",
            self.category,
            f"{self.operation_name}完成",
            operation=self.operation_name,
            duration_ms=total_time * 1000,
            checkpoints=self.checkpoints,
            action="finish"
        )
        
        return self.checkpoints.copy()
    
    def get_time(self) -> float:
        """获取从开始到当前的总时间"""
        if not self.start_time:
            return 0.0
        return time.time() - self.start_time
    
    def get_total_time(self) -> float:
        """获取总时间（如果已完成）或当前时间"""
        if 'total' in self.checkpoints:
            return self.checkpoints['total']
        return self.get_time()
    
    def get_checkpoint_count(self) -> int:
        """获取检查点数量"""
        return len([k for k in self.checkpoints.keys() if k != 'total'])
    
    def get_average_time(self) -> float:
        """获取平均每个检查点的时间"""
        count = self.get_checkpoint_count()
        if count == 0:
            return 0.0
        total = self.get_total_time()
        return total / count
    
    def get_time_breakdown(self) -> Dict[str, float]:
        """获取时间分解信息"""
        return self.checkpoints.copy()


class ProgressLogger:
    """进度日志器"""
    
    @staticmethod
    def log_progress(current: int, total: int, prefix: str = "处理进度", 
                    category: str = "debug", show_percentage: bool = True) -> None:
        """记录进度"""
        percentage = (current / total * 100) if total > 0 else 0
        
        _log_manager.log(
            "INFO",
            category,
            f"{prefix}更新",
            current=current,
            total=total,
            percentage=percentage,
            action="progress"
        )
    
    @staticmethod
    def log_batch_progress(batch_idx: int, total_batches: int, batch_size: int, 
                          prefix: str = "批次进度", category: str = "debug") -> None:
        """记录批次进度"""
        _log_manager.log(
            "INFO",
            category,
            f"{prefix}更新",
            batch_idx=batch_idx + 1,
            total_batches=total_batches,
            batch_size=batch_size,
            action="batch_progress"
        )
    
    @staticmethod
    def finish_progress(category: str = "debug") -> None:
        """完成进度显示"""
        _log_manager.log(
            "INFO",
            category,
            "进度记录完成",
            action="progress_finish"
        )


class RequestLogger:
    """请求日志器"""
    
    @staticmethod
    def log_request_info(request_obj: Any, raw_request: Optional[Request] = None,
                        category: str = "search") -> None:
        """记录请求信息"""
        metadata = {}
        
        # 处理Pydantic模型
        if hasattr(request_obj, "__dict__"):
            try:
                metadata["model_type"] = type(request_obj).__name__
                if hasattr(request_obj, 'dict'):
                    metadata["request_data"] = request_obj.dict()
                elif hasattr(request_obj, 'model_dump'):
                    metadata["request_data"] = request_obj.model_dump()
                else:
                    metadata["request_data"] = str(request_obj)
            except Exception as e:
                metadata["model_error"] = str(e)
        
        # 处理原始请求
        if raw_request:
            try:
                metadata["method"] = raw_request.method
                metadata["url"] = str(raw_request.url)
                metadata["headers"] = dict(raw_request.headers)
            except Exception as e:
                metadata["raw_request_error"] = str(e)
        
        _log_manager.log(
            "INFO",
            category,
            "收到请求",
            **metadata,
            action="request_received"
        )
    
    @staticmethod
    async def log_raw_request_body(raw_request: Request, category: str = "debug") -> None:
        """记录原始请求体"""
        try:
            body = await raw_request.body()
            body_str = body.decode('utf-8', errors='replace')
            
            metadata = {
                "body_length": len(body),
                "content_type": raw_request.headers.get('content-type'),
                "action": "raw_request_body"
            }
            
            try:
                json_data = json.loads(body_str)
                metadata["parsed_json"] = json_data
                metadata["json_fields"] = {k: type(v).__name__ for k, v in json_data.items()}
            except json.JSONDecodeError as json_err:
                metadata["json_error"] = str(json_err)
                metadata["json_error_pos"] = getattr(json_err, 'pos', None)
                metadata["raw_body_preview"] = body_str[:200]
            
            _log_manager.log("DEBUG", category, "原始请求体", **metadata)
            
        except Exception as e:
            _log_manager.log(
                "ERROR", 
                "error", 
                "无法读取原始请求体",
                error=str(e),
                error_type=type(e).__name__,
                traceback=traceback.format_exc()
            )


class OperationLogger:
    """操作日志器"""
    
    @staticmethod
    def log_operation_start(operation_name: str, category: str = "debug", **kwargs) -> None:
        """记录操作开始"""
        _log_manager.log(
            "INFO",
            category,
            f"{operation_name}开始",
            operation=operation_name,
            action="operation_start",
            **kwargs
        )
    
    @staticmethod
    def log_operation_success(operation_name: str, category: str = "debug", **kwargs) -> None:
        """记录操作成功"""
        _log_manager.log(
            "INFO",
            category,
            f"{operation_name}成功",
            operation=operation_name,
            action="operation_success",
            **kwargs
        )
    
    @staticmethod
    def log_operation_error(operation_name: str, error: Exception, 
                           category: str = "error", **kwargs) -> None:
        """记录操作错误"""
        _log_manager.log(
            "ERROR",
            category,
            f"{operation_name}失败",
            operation=operation_name,
            action="operation_error",
            error=str(error),
            error_type=type(error).__name__,
            traceback=traceback.format_exc(),
            **kwargs
        )
    
    @staticmethod
    def log_step(step_name: str, details: str = "", category: str = "debug") -> None:
        """记录操作步骤"""
        _log_manager.log(
            "INFO",
            category,
            f"执行步骤: {step_name}",
            step=step_name,
            details=details,
            action="step"
        )
    
    @staticmethod
    def log_connection_attempt(database: Optional[str] = None, category: str = "debug") -> None:
        """记录数据库连接尝试"""
        db_name = database or '默认数据库'
        _log_manager.log(
            "INFO",
            category,
            "尝试连接数据库",
            database=db_name,
            action="connection_attempt"
        )
    
    @staticmethod
    def log_connection_success(database: Optional[str] = None, 
                              connection_time: Optional[float] = None,
                              category: str = "debug") -> None:
        """记录数据库连接成功"""
        db_name = database or '默认数据库'
        metadata: Dict[str, Any] = {
            "database": db_name,
            "action": "connection_success"
        }
        if connection_time:
            metadata["duration_ms"] = connection_time * 1000
        
        _log_manager.log("INFO", category, "数据库连接成功", **metadata)
    
    @staticmethod
    def log_connection_error(database: Optional[str] = None, 
                            error: Optional[Exception] = None,
                            category: str = "error") -> None:
        """记录数据库连接错误"""
        db_name = database or '默认数据库'
        metadata = {
            "database": db_name,
            "action": "connection_error"
        }
        if error:
            metadata.update({
                "error": str(error),
                "error_type": type(error).__name__,
                "traceback": traceback.format_exc()
            })
        
        _log_manager.log("ERROR", category, "数据库连接失败", **metadata)


class ResultLogger:
    """结果日志器"""
    
    @staticmethod
    def log_search_results_summary(results: list, max_preview: int = 3,
                                  category: str = "search") -> None:
        """记录搜索结果摘要"""
        if not results:
            _log_manager.log(
                "INFO",
                category,
                "搜索无结果",
                result_count=0,
                action="search_results"
            )
            return
        
        # 提取结果摘要
        result_summaries = []
        for i, result in enumerate(results[:max_preview]):
            summary = {
                "index": i + 1,
                "content_preview": str(result.get('content', ''))[:50],
                "similarity": result.get('similarity', result.get('distance', 0)),
                "collection": result.get('collection', '')
            }
            result_summaries.append(summary)
        
        _log_manager.log(
            "INFO",
            category,
            "搜索结果",
            result_count=len(results),
            result_previews=result_summaries,
            total_shown=min(len(results), max_preview),
            action="search_results"
        )
    
    @staticmethod
    def log_vector_generation(text: str, vector_dim: int, generation_time: float,
                             category: str = "performance") -> None:
        """记录向量生成信息"""
        text_preview = text[:50] + '...' if len(text) > 50 else text
        _log_manager.log(
            "PERF",
            category,
            "向量生成完成",
            text_preview=text_preview,
            text_length=len(text),
            vector_dim=vector_dim,
            duration_ms=generation_time * 1000,
            action="vector_generation"
        )


class ParameterLogger:
    """参数日志器"""
    
    @staticmethod
    def log_parameter_processing(param_name: str, original_value: Any, 
                                processed_value: Any, category: str = "debug") -> None:
        """记录参数处理"""
        _log_manager.log(
            "DEBUG",
            category,
            "参数处理",
            param_name=param_name,
            original_value=str(original_value),
            processed_value=str(processed_value),
            original_type=type(original_value).__name__,
            processed_type=type(processed_value).__name__,
            action="param_processing"
        )
    
    @staticmethod
    def log_validation_result(param_name: str, value: Any, is_valid: bool, 
                             error_msg: str = "", category: str = "debug") -> None:
        """记录验证结果"""
        level = "INFO" if is_valid else "WARN"
        message = f"参数验证{'成功' if is_valid else '失败'}"
        
        metadata = {
            "param_name": param_name,
            "value": str(value),
            "is_valid": is_valid,
            "action": "param_validation"
        }
        
        if not is_valid and error_msg:
            metadata["error_msg"] = error_msg
        
        _log_manager.log(level, category, message, **metadata)


class DebugLogger:
    """调试日志器"""
    
    @staticmethod
    def log_debug_info(tag: str, info: Any, category: str = "debug") -> None:
        """记录调试信息"""
        _log_manager.log(
            "DEBUG",
            category,
            f"调试信息: {tag}",
            tag=tag,
            info=str(info),
            info_type=type(info).__name__,
            action="debug_info"
        )
    
    @staticmethod
    def log_object_details(obj: Any, obj_name: str = "对象", category: str = "debug") -> None:
        """记录对象详细信息"""
        metadata: Dict[str, Any] = {
            "object_name": obj_name,
            "object_type": type(obj).__name__,
            "object_value": str(obj),
            "action": "object_details"
        }
        
        if hasattr(obj, '__dict__'):
            metadata["object_attrs"] = {k: type(v).__name__ for k, v in obj.__dict__.items()}
        
        _log_manager.log("DEBUG", category, "对象详情", **metadata)
    
    @staticmethod
    def log_method_call(obj: Any, method_name: str, *args, 
                       category: str = "debug", **kwargs) -> None:
        """记录方法调用"""
        metadata = {
            "object_type": type(obj).__name__,
            "method_name": method_name,
            "method_exists": hasattr(obj, method_name),
            "action": "method_call"
        }
        
        if args:
            metadata["args"] = [str(arg) for arg in args]
        if kwargs:
            metadata["kwargs"] = {k: str(v) for k, v in kwargs.items()}
        
        _log_manager.log("DEBUG", category, "方法调用", **metadata)


# 便捷的上下文管理器
class LoggedOperation:
    """带日志的操作上下文管理器"""
    
    def __init__(self, operation_name: str, category: str = "debug", **kwargs):
        self.operation_name = operation_name
        self.category = category
        self.kwargs = kwargs
        self.time_tracker = TimeTracker(operation_name, category)
    
    def __enter__(self):
        OperationLogger.log_operation_start(self.operation_name, self.category, **self.kwargs)
        self.time_tracker.start()
        return self.time_tracker
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        time_details = self.time_tracker.finish()
        
        if exc_type is None:
            OperationLogger.log_operation_success(
                self.operation_name,
                self.category,
                总耗时秒=time_details.get('total', 0)
            )
        else:
            OperationLogger.log_operation_error(
                self.operation_name,
                exc_val,
                self.category,
                总耗时秒=time_details.get('total', 0)
            )
        
        return False  # 不抑制异常


# 便捷函数
def log_request_details(request_obj: Any, raw_request: Optional[Request] = None,
                       category: str = "search") -> None:
    """记录请求详情的便捷函数"""
    RequestLogger.log_request_info(request_obj, raw_request, category)


def track_time(operation_name: str, category: str = "performance") -> TimeTracker:
    """创建时间追踪器的便捷函数"""
    tracker = TimeTracker(operation_name, category)
    tracker.start()
    return tracker


def log_with_context(operation_name: str, category: str = "debug", **kwargs):
    """创建带日志的操作上下文的便捷函数"""
    return LoggedOperation(operation_name, category, **kwargs)


def configure_logging(config: Optional[LogConfig] = None):
    """配置日志系统"""
    global _log_manager
    # 关闭现有的日志管理器
    if _log_manager:
        _log_manager.close()
    # 重置LogManager的单例
    LogManager._instance = None
    _log_manager = LogManager(config)


def shutdown_logging():
    """关闭日志系统"""
    global _log_manager
    if _log_manager:
        _log_manager.close()


# 程序退出时自动关闭日志系统
import atexit
atexit.register(shutdown_logging) 