"""
日志配置模块
统一管理应用的日志配置
"""
import logging
import logging.config
import sys
from pathlib import Path
from typing import Dict, Any


def setup_logging(level: str = "INFO", log_dir: str = "logs") -> None:
    """
    设置应用日志配置
    
    Args:
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
        log_dir: 日志目录
    """
    # 创建日志目录
    Path(log_dir).mkdir(exist_ok=True)
    
    # 日志配置
    config = get_logging_config(level, log_dir)
    
    # 应用配置
    logging.config.dictConfig(config)
    
    # 设置root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))


def get_logging_config(level: str, log_dir: str) -> Dict[str, Any]:
    """获取日志配置字典"""
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "detailed": {
                "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "simple": {
                "format": "[%(levelname)s] %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": level,
                "formatter": "standard",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": level,
                "formatter": "detailed",
                "filename": f"{log_dir}/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": f"{log_dir}/error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8"
            }
        },
        "loggers": {
            "app": {
                "level": level,
                "handlers": ["console", "file", "error_file"],
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["console", "error_file"],
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            }
        },
        "root": {
            "level": level,
            "handlers": ["console", "file"]
        }
    }


def get_logger(name: str) -> logging.Logger:
    """获取logger实例"""
    return logging.getLogger(name) 