"""
参数验证工具模块
提供统一的参数验证功能
"""
from typing import List, Any, Union, Optional, Dict
from fastapi import HTTPException


class ValidationError(Exception):
    """自定义验证错误"""
    pass


class ParameterValidator:
    """参数验证器"""
    
    @staticmethod
    def validate_top_k(top_k: Union[str, int]) -> int:
        """
        验证top_k参数
        
        Args:
            top_k: 返回结果数量
            
        Returns:
            int: 验证后的top_k值
            
        Raises:
            ValidationError: 参数验证失败
        """
        try:
            # 如果是字符串，尝试转换为整数
            if isinstance(top_k, str):
                top_k_str = top_k.strip().split('?')[0]  # 移除可能的特殊字符
                top_k = int(top_k_str)
            
            # 验证参数范围
            if top_k <= 0:
                raise ValidationError("top_k必须大于0")
            
            if top_k > 1000:  # 设置合理的上限
                raise ValidationError("top_k不能超过1000")
                
            return top_k
            
        except ValueError as e:
            raise ValidationError(f"top_k必须是整数，无法将'{top_k}'转换为整数")
    
    @staticmethod
    def validate_chunk_parameters(chunk_size: Union[str, int], chunk_overlap: Union[str, int]) -> tuple[int, int]:
        """
        验证分块参数
        
        Args:
            chunk_size: 分块大小
            chunk_overlap: 分块重叠大小
            
        Returns:
            tuple: (chunk_size_int, chunk_overlap_int)
            
        Raises:
            ValidationError: 参数验证失败
        """
        try:
            # 处理可能的特殊字符
            if isinstance(chunk_size, str):
                chunk_size_str = chunk_size.strip().split('?')[0]
                chunk_size_int = int(chunk_size_str)
            else:
                chunk_size_int = int(chunk_size)
                
            if isinstance(chunk_overlap, str):
                chunk_overlap_str = chunk_overlap.strip().split('?')[0]
                chunk_overlap_int = int(chunk_overlap_str)
            else:
                chunk_overlap_int = int(chunk_overlap)

            # 验证参数范围
            if chunk_size_int <= 0:
                raise ValidationError("分块大小必须大于0")
            if chunk_overlap_int < 0:
                raise ValidationError("分块重叠必须大于等于0")
            if chunk_overlap_int >= chunk_size_int:
                raise ValidationError("分块重叠必须小于分块大小")
                
            return chunk_size_int, chunk_overlap_int
            
        except ValueError as e:
            raise ValidationError(f"分块参数错误: {str(e)}")
    
    @staticmethod
    def validate_file_type(filename: str, allowed_types: Optional[List[str]] = None) -> str:
        """
        验证文件类型
        
        Args:
            filename: 文件名
            allowed_types: 允许的文件类型列表
            
        Returns:
            str: 文件类型
            
        Raises:
            ValidationError: 文件类型不支持
        """
        if not filename:
            raise ValidationError("文件名不能为空")
            
        if allowed_types is None:
            allowed_types = ['pdf', 'docx', 'txt']
            
        file_type = filename.split('.')[-1].lower()
        
        if file_type not in allowed_types:
            raise ValidationError(f"不支持的文档类型: {file_type}，支持的类型: {', '.join(allowed_types)}")
            
        return file_type
    
    @staticmethod
    def validate_ids_consistency(ids: Optional[List[Any]], texts: List[str]) -> None:
        """
        验证ID列表与文本列表的一致性
        
        Args:
            ids: ID列表
            texts: 文本列表
            
        Raises:
            ValidationError: ID列表与文本列表长度不一致
        """
        if ids is not None:
            if len(ids) != len(texts):
                raise ValidationError(f"ids列表长度({len(ids)})必须与texts列表长度({len(texts)})一致")
            
            # 检查是否有重复ID
            if len(set(ids)) != len(ids):
                raise ValidationError("存在重复的自定义ID")
    
    @staticmethod
    def validate_batch_size(batch_size: Union[str, int]) -> int:
        """
        验证批次大小
        
        Args:
            batch_size: 批次大小
            
        Returns:
            int: 验证后的批次大小
            
        Raises:
            ValidationError: 参数验证失败
        """
        try:
            if isinstance(batch_size, str):
                batch_size = int(batch_size)
            
            # 限制批次大小在合理范围内
            batch_size = max(1, min(batch_size, 50))
            return batch_size
            
        except ValueError:
            raise ValidationError(f"batch_size必须是整数")
    
    @staticmethod
    def validate_vector_dimension(expected_dim: int, actual_dim: int) -> None:
        """
        验证向量维度
        
        Args:
            expected_dim: 期望的向量维度
            actual_dim: 实际的向量维度
            
        Raises:
            ValidationError: 向量维度不匹配
        """
        if expected_dim != actual_dim:
            raise ValidationError(f"向量维度不匹配: 期望{expected_dim}，实际{actual_dim}")
    
    @staticmethod
    def validate_weight_parameters(dense_weight: float, sparse_weight: float) -> tuple[float, float]:
        """
        验证权重参数
        
        Args:
            dense_weight: 密集向量权重
            sparse_weight: 稀疏向量权重
            
        Returns:
            tuple: (dense_weight, sparse_weight)
            
        Raises:
            ValidationError: 权重参数验证失败
        """
        if not (0 <= dense_weight <= 1):
            raise ValidationError("dense_weight必须在0-1之间")
            
        if not (0 <= sparse_weight <= 1):
            raise ValidationError("sparse_weight必须在0-1之间")
            
        # 检查权重和是否合理（允许一定误差）
        weight_sum = dense_weight + sparse_weight
        if abs(weight_sum - 1.0) > 0.1:
            raise ValidationError(f"权重和应该接近1.0，当前为{weight_sum:.2f}")
            
        return dense_weight, sparse_weight
    
    @staticmethod
    def validate_rrf_k(rrf_k: int) -> int:
        """
        验证RRF参数
        
        Args:
            rrf_k: RRF平滑参数
            
        Returns:
            int: 验证后的rrf_k值
            
        Raises:
            ValidationError: 参数验证失败
        """
        if not (10 <= rrf_k <= 100):
            raise ValidationError("rrf_k必须在10-100之间")
            
        return rrf_k


class FileValidator:
    """文件验证器"""
    
    @staticmethod
    def validate_pdf_header(content: bytes) -> None:
        """验证PDF文件头"""
        if not content.startswith(b'%PDF-'):
            raise ValidationError("无效的PDF文件格式")
    
    @staticmethod
    def validate_docx_header(content: bytes) -> None:
        """验证DOCX文件头"""
        if not content.startswith(b'PK\x03\x04'):
            raise ValidationError("无效的DOCX文件格式")
    
    @staticmethod
    def validate_content_not_empty(text: str) -> None:
        """验证内容不为空"""
        if not text.strip():
            raise ValidationError("文档内容解析为空")


class ListValidator:
    """列表验证器"""
    
    @staticmethod
    def validate_not_empty(items: List[Any], field_name: str = "列表") -> None:
        """验证列表不为空"""
        if not items:
            raise ValidationError(f"{field_name}不能为空")
    
    @staticmethod
    def validate_unique_ids(ids: List[Any]) -> None:
        """验证ID列表唯一性"""
        if len(set(ids)) != len(ids):
            raise ValidationError("存在重复的ID")


def convert_validation_error_to_http_exception(func):
    """
    装饰器：将ValidationError转换为HTTPException
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            raise HTTPException(400, f"参数验证失败: {str(e)}")
    return wrapper


# 便捷的验证函数
@convert_validation_error_to_http_exception
def validate_search_parameters(top_k: Union[str, int], total_results: Optional[Union[str, int]] = None) -> tuple[int, Optional[int]]:
    """
    验证搜索相关参数
    
    Args:
        top_k: 返回结果数量
        total_results: 总结果数量（可选）
        
    Returns:
        tuple: (top_k, total_results)
    """
    validated_top_k = ParameterValidator.validate_top_k(top_k)
    
    validated_total_results = None
    if total_results is not None:
        validated_total_results = ParameterValidator.validate_top_k(total_results)
    
    return validated_top_k, validated_total_results


@convert_validation_error_to_http_exception
def validate_upload_parameters(chunk_size: Union[str, int], chunk_overlap: Union[str, int]) -> tuple[int, int]:
    """
    验证上传相关参数
    
    Args:
        chunk_size: 分块大小
        chunk_overlap: 分块重叠大小
        
    Returns:
        tuple: (chunk_size, chunk_overlap)
    """
    return ParameterValidator.validate_chunk_parameters(chunk_size, chunk_overlap) 