#!/usr/bin/env python3
import requests
import time

url = 'http://copilot.csvw.com/rag_service/api/v1/search'
data = {
    'text': '字符串ID',
    'collection': 'test_string_id_v2',
    'database': 'test_db',
    'top_k': 5
}

time.sleep(2)  # 等待索引
response = requests.post(url, json=data)
print('状态码:', response.status_code)
result = response.json()
print('查询:', result.get('query'))
print('结果数量:', result.get('total_results'))

for i, res in enumerate(result.get('results', []), 1):
    print(f'结果 {i}: ID={res.get("id")}, 内容={res.get("content")}')
