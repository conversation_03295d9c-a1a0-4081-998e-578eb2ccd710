import os
import asyncio
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB

# Load environment variables
load_dotenv()

# Get Milvus connection parameters from environment
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

async def migrate_collections():
    """
    迁移现有集合到JSON metadata格式
    """
    print("=== Milvus集合迁移工具 ===")
    print("此工具将帮助您将现有的字符串metadata集合迁移到新的JSON格式")
    print()
    
    # 输入database名称
    database = input("请输入Milvus数据库名称: ").strip()
    
    # Create MilvusVectorDB instance
    db = MilvusVectorDB(uri=uri, token=token, database=database)
    
    try:
        # Connect to Milvus
        await db.connect()
        print("✅ 成功连接到Milvus")
        
        # 获取所有集合
        await db._refresh_collections()
        collections = list(db.collections_cache)
        
        if not collections:
            print("❌ 没有找到任何集合")
            return
        
        print(f"\n📋 找到以下集合:")
        for i, collection in enumerate(collections, 1):
            print(f"  {i}. {collection}")
        
        print("\n🔄 迁移选项:")
        print("1. 迁移所有集合")
        print("2. 迁移指定集合")
        print("3. 仅测试兼容性（不创建新集合）")
        print("4. 退出")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == "1":
            # 迁移所有集合
            print(f"\n🚀 开始迁移所有 {len(collections)} 个集合...")
            for collection in collections:
                try:
                    new_collection = await db.migrate_collection_to_json_metadata(collection)
                    print(f"✅ 集合 '{collection}' 迁移完成 -> '{new_collection}'")
                except Exception as e:
                    print(f"❌ 集合 '{collection}' 迁移失败: {e}")
        
        elif choice == "2":
            # 迁移指定集合
            collection_name = input("\n请输入要迁移的集合名称: ").strip()
            if collection_name not in collections:
                print(f"❌ 集合 '{collection_name}' 不存在")
                return
            
            new_name = input(f"请输入新集合名称 (留空使用 '{collection_name}_json'): ").strip()
            new_name = new_name if new_name else None
            
            try:
                new_collection = await db.migrate_collection_to_json_metadata(collection_name, new_name)
                print(f"✅ 集合迁移完成: '{collection_name}' -> '{new_collection}'")
            except Exception as e:
                print(f"❌ 迁移失败: {e}")
        
        elif choice == "3":
            # 测试兼容性
            print(f"\n🧪 测试现有数据的兼容性...")
            for collection in collections:
                try:
                    # 查询少量数据测试
                    results = await db.search_vectors(
                        collection=collection,
                        vector=[0.1] * 384,  # 假设的查询向量
                        top_k=1
                    )
                    if results:
                        metadata = results[0].get('metadata', {})
                        if isinstance(metadata, dict):
                            print(f"✅ 集合 '{collection}': metadata已是JSON格式")
                        else:
                            print(f"🔄 集合 '{collection}': metadata是字符串格式，需要迁移")
                    else:
                        print(f"ℹ️  集合 '{collection}': 没有数据")
                except Exception as e:
                    print(f"❌ 集合 '{collection}': 测试失败 - {e}")
        
        elif choice == "4":
            print("👋 退出迁移工具")
            return
        
        else:
            print("❌ 无效选择")
            return
        
        print("\n✨ 迁移操作完成！")
        print("\n📝 注意事项:")
        print("1. 原集合数据保持不变")
        print("2. 新集合使用JSON格式的metadata字段")
        print("3. 新集合支持更强大的JSON查询功能")
        print("4. 建议测试新集合功能正常后再删除原集合")
        
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

async def test_compatibility():
    """
    测试现有数据的兼容性
    """
    print("=== 兼容性测试 ===")
    
    db = MilvusVectorDB(uri=uri, token=token)
    
    try:
        await db.connect()
        print("✅ 连接成功")
        
        # 获取所有集合
        await db._refresh_collections()
        collections = list(db.collections_cache)
        
        print(f"\n📋 测试 {len(collections)} 个集合的兼容性:")
        
        for collection in collections:
            try:
                # 尝试搜索
                results = await db.search_vectors(
                    collection=collection,
                    vector=[0.1] * 384,  # 假设向量
                    top_k=1
                )
                
                if results:
                    result = results[0]
                    metadata = result.get('metadata', {})
                    print(f"✅ {collection}: 兼容 (metadata类型: {type(metadata).__name__})")
                else:
                    print(f"ℹ️  {collection}: 无数据")
                    
            except Exception as e:
                print(f"❌ {collection}: 错误 - {e}")
        
        print("\n✨ 兼容性测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("选择操作:")
    print("1. 数据迁移")
    print("2. 兼容性测试")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        asyncio.run(migrate_collections())
    elif choice == "2":
        asyncio.run(test_compatibility())
    else:
        print("无效选择")
