"""
API数据模型层 (Schemas)

该层负责定义所有API的请求和响应数据模型
遵循分层架构设计原则，与routes层分离

组织结构：
- requests.py: 所有请求模型
- responses.py: 所有响应模型  
- common.py: 通用模型和基类
"""

# 请求模型
from .requests import (
    TextUploadRequest,
    BatchTextItem,
    BatchTextUploadRequest,
    HybridTextUploadRequest,
    SearchRequest,
    DBSearchConfig,
    MultiDBSearchRequest,
    HybridMultiDBSearchRequest,
    UpsertRequest,
    DeleteRequest,
    EmbeddingRequest
)

# 响应模型
from .responses import (
    SearchResult
)

# 通用模型
from .common import (
    ChunkStrategy
)

__all__ = [
    # 请求模型
    "TextUploadRequest",
    "BatchTextItem", 
    "BatchTextUploadRequest",
    "HybridTextUploadRequest",
    "SearchRequest",
    "DBSearchConfig",
    "MultiDBSearchRequest",
    "HybridMultiDBSearchRequest",
    "UpsertRequest",
    "DeleteRequest",
    "EmbeddingRequest",
    
    # 响应模型
    "SearchResult",
    
    # 通用模型
    "ChunkStrategy"
] 