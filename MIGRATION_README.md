# Milvus向量数据库迁移工具

本工具支持在不重新进行向量化的情况下，将数据从源Milvus数据库迁移到目标Milvus数据库。

## 功能特性

✅ **无需重新向量化** - 直接迁移已有的向量数据  
✅ **断点续传** - 支持中断后继续迁移  
✅ **进度监控** - 实时显示迁移进度  
✅ **批量处理** - 分批迁移，避免内存溢出  
✅ **数据验证** - 迁移后自动验证数据完整性  
✅ **错误恢复** - 自动处理迁移过程中的错误  
✅ **日志记录** - 详细的迁移日志  

## 文件说明

- `migrate_milvus_data.py` - 基础迁移脚本
- `advanced_migration.py` - 高级迁移脚本（推荐使用）
- `migration_config.py` - 迁移配置文件（需要创建）
- `migration_config_example.py` - 配置示例文件
- `setup_migration_config.py` - 配置设置工具（推荐使用）
- `MIGRATION_README.md` - 使用说明文档

## 使用步骤

### 方法一：使用配置设置工具（推荐）

运行配置设置工具，按提示创建配置文件：
```bash
python setup_migration_config.py
```

### 方法二：手动创建配置文件

#### 1. 创建配置文件
复制示例配置文件并重命名：
```bash
cp migration_config_example.py migration_config.py
```

#### 2. 配置目标数据库
编辑 `migration_config.py` 文件，填写目标Milvus数据库的连接信息：

```python
# 目标数据库配置（必须填写）
TARGET_CONFIG = {
    "uri": "https://your-target-milvus.com:19530",  # 目标数据库URI
    "token": "your-target-token",  # 目标数据库Token（如果需要）
    "database": "your-target-database",  # 目标数据库名称（可选）
}

# 源数据库配置（可选，留空则从环境变量读取）
SOURCE_CONFIG = {
    "uri": "",  # 留空则从 VECTOR_DB_URI 环境变量读取
    "token": "",  # 留空则从 VECTOR_DB_TOKEN 环境变量读取
    "database": "",  # 留空则从 VECTOR_DB_DATABASE 环境变量读取
}
```

### 3. 验证配置

运行配置验证：
```bash
python migration_config.py
```

### 4. 运行迁移脚本

#### 基础迁移（简单场景）
```bash
python migrate_milvus_data.py
```

#### 高级迁移（推荐，支持断点续传）
```bash
python advanced_migration.py
```

### 5. 选择迁移选项

运行脚本后，您可以选择：
- 迁移所有集合
- 迁移指定集合
- 查看迁移进度
- 清除进度记录

## 配置说明

### 源数据库配置
源数据库配置从环境变量读取：
- `VECTOR_DB_URI` - 源数据库URI
- `VECTOR_DB_TOKEN` - 源数据库Token
- `VECTOR_DB_DATABASE` - 源数据库名称

### 迁移参数配置
在 `migration_config.py` 中可以调整：

```python
MIGRATION_CONFIG = {
    "batch_size": 1000,  # 每批迁移的记录数
    "verify_after_migration": True,  # 迁移后是否验证数据
    "skip_existing_collections": True,  # 是否跳过已存在的集合
    "preserve_collection_names": True,  # 是否保持原集合名称
}
```

## 迁移原理

1. **连接验证** - 连接源数据库和目标数据库
2. **集合分析** - 获取源集合的schema和数据统计
3. **集合创建** - 在目标数据库创建相同结构的集合
4. **数据迁移** - 分批读取源数据并写入目标数据库
5. **数据验证** - 比较源和目标的记录数量
6. **进度保存** - 保存迁移进度，支持断点续传

## 数据结构支持

支持迁移的字段类型：
- `vector` - 向量数据（核心）
- `content` - 文本内容
- `collection_name` - 集合名称
- `metadata` - 元数据（JSON格式）
- 其他自定义字段

## 注意事项

### ⚠️ 迁移前准备
1. 确保目标数据库有足够的存储空间
2. 确保网络连接稳定
3. 建议在低峰期进行迁移
4. 备份重要数据

### ⚠️ 性能优化
- 调整 `batch_size` 参数以优化性能
- 大数据量迁移建议使用高级迁移脚本
- 监控内存和网络使用情况

### ⚠️ 错误处理
- 迁移过程中如果出现错误，可以重新运行脚本继续迁移
- 检查日志文件 `migration.log` 获取详细错误信息
- 网络中断后可以从断点继续迁移

## 常见问题

### Q: 迁移过程中断了怎么办？
A: 重新运行高级迁移脚本，它会自动从上次中断的位置继续迁移。

### Q: 如何验证迁移是否成功？
A: 脚本会自动验证迁移后的数据量是否一致，也可以手动对比源和目标数据库。

### Q: 可以迁移部分集合吗？
A: 可以，运行脚本时选择"迁移指定集合"选项。

### Q: 迁移会影响源数据库吗？
A: 不会，迁移过程只读取源数据库，不会修改或删除任何数据。

### Q: 目标数据库已有同名集合怎么办？
A: 默认会跳过已存在的集合，可以在配置中修改此行为。

## 示例配置

### 本地Milvus迁移
```python
TARGET_CONFIG = {
    "uri": "http://localhost:19530",
    "token": "",
    "database": "default"
}
```

### 云端Milvus迁移
```python
TARGET_CONFIG = {
    "uri": "https://your-cloud-milvus.com:19530",
    "token": "your-cloud-token",
    "database": "production"
}
```

### Zilliz Cloud迁移
```python
TARGET_CONFIG = {
    "uri": "https://your-cluster.zillizcloud.com:19530",
    "token": "your-zilliz-token",
    "database": "default"
}
```

## 技术支持

如果在迁移过程中遇到问题，请：
1. 查看 `migration.log` 日志文件
2. 检查网络连接和数据库配置
3. 确认源和目标数据库版本兼容性
4. 联系技术支持团队

---

**重要提醒**: 在生产环境迁移前，请先在测试环境验证迁移脚本的正确性。
