# 工具层 (Utils) 说明文档

## 概述

工具层提供了一系列统一的工具类和函数，用于支持整个应用的通用功能。这些工具类旨在提高代码的复用性、可维护性和一致性。

## 模块结构

```
app/utils/
├── __init__.py              # 包初始化文件
├── validation.py            # 参数验证工具
├── formatting.py            # 结果格式化工具
├── filter_utils.py          # 过滤表达式构建工具
├── logging_utils.py         # 日志记录工具
├── response_utils.py        # 响应处理工具
├── data_utils.py           # 数据处理工具
└── README.md               # 本说明文档
```

## 各模块功能详解

### 1. validation.py - 参数验证工具

**主要类：**
- `ParameterValidator`: 核心参数验证器
- `FileValidator`: 文件验证器
- `ListValidator`: 列表验证器

**主要功能：**
- `validate_top_k()`: 验证搜索返回数量参数
- `validate_chunk_parameters()`: 验证文本分块参数
- `validate_file_type()`: 验证文件类型
- `validate_ids_consistency()`: 验证ID列表一致性
- `validate_vector_dimension()`: 验证向量维度
- `validate_weight_parameters()`: 验证权重参数

**使用示例：**
```python
from app.utils.validation import validate_search_parameters, ParameterValidator

# 验证搜索参数
top_k, total_results = validate_search_parameters(5, 10)

# 验证文件类型
file_type = ParameterValidator.validate_file_type("document.pdf")
```

### 2. formatting.py - 结果格式化工具

**主要类：**
- `TimeFormatter`: 时间格式化工具
- `SearchResultFormatter`: 搜索结果格式化器
- `ResponseFormatter`: 响应格式化器
- `ProgressFormatter`: 进度格式化器
- `MetadataFormatter`: 元数据格式化器

**主要功能：**
- 统一的时间格式化
- 搜索结果的标准化格式化
- 自动解密加密内容
- 元数据解析和合并
- 进度信息格式化

**使用示例：**
```python
from app.utils.formatting import SearchResultFormatter, TimeFormatter

# 格式化搜索结果
formatted_results = SearchResultFormatter.format_search_results(raw_results)

# 格式化时间
duration_str = TimeFormatter.format_duration(3.14159)  # "3.142秒"
```

### 3. filter_utils.py - 过滤表达式构建工具

**主要类：**
- `FilterExpressionBuilder`: 过滤表达式构建器
- `RequestFieldExtractor`: 请求字段提取器
- `FilterValidator`: 过滤条件验证器

**主要功能：**
- 构建Milvus筛选表达式
- 支持metadata字段筛选
- 支持直接字段筛选
- 从请求对象提取动态字段
- 表达式语法验证

**使用示例：**
```python
from app.utils.filter_utils import build_combined_filter_expression, get_search_standard_fields

# 构建组合过滤表达式
filter_expr = build_combined_filter_expression(
    request_obj, 
    get_search_standard_fields()
)
```

### 4. logging_utils.py - 日志记录工具

**主要类：**
- `TimeTracker`: 时间追踪器
- `ProgressLogger`: 进度日志器
- `RequestLogger`: 请求日志器
- `OperationLogger`: 操作日志器
- `ResultLogger`: 结果日志器
- `LoggedOperation`: 带日志的操作上下文管理器

**主要功能：**
- 操作时间追踪和统计
- 进度显示和记录
- 详细的请求信息记录
- 操作成功/失败日志
- 搜索结果摘要记录

**使用示例：**
```python
from app.utils.logging_utils import track_time, log_with_context

# 使用时间追踪器
tracker = track_time("向量搜索")
tracker.checkpoint("向量生成")
tracker.checkpoint("数据库查询")
time_details = tracker.finish()

# 使用日志上下文管理器
with log_with_context("文档上传", 文件名="test.pdf") as tracker:
    # 执行操作
    tracker.checkpoint("文件解析")
    tracker.checkpoint("向量生成")
```

### 5. response_utils.py - 响应处理工具

**主要类：**
- `ResponseBuilder`: 通用响应构建器
- `UploadResponseBuilder`: 上传响应构建器
- `SearchResponseBuilder`: 搜索响应构建器
- `VectorOperationResponseBuilder`: 向量操作响应构建器
- `ErrorResponseBuilder`: 错误响应构建器

**主要功能：**
- 统一的响应格式
- 成功/错误响应构建
- 分页响应支持
- 专业的错误分类和处理

**使用示例：**
```python
from app.utils.response_utils import SearchResponseBuilder, ErrorResponseBuilder

# 构建搜索响应
response = SearchResponseBuilder.search_response(
    query="测试查询",
    results=formatted_results,
    search_time="0.123秒"
)

# 构建错误响应
error_response = ErrorResponseBuilder.validation_error_response("参数无效")
```

### 6. data_utils.py - 数据处理工具

**主要类：**
- `DataConverter`: 数据类型转换器
- `ListProcessor`: 列表处理器
- `TextProcessor`: 文本处理器
- `MetadataProcessor`: 元数据处理器
- `RecordBuilder`: 记录构建器
- `IDGenerator`: ID生成器
- `FieldExtractor`: 字段提取器

**主要功能：**
- 安全的数据类型转换
- 列表分块和去重
- 文本分块和清理
- 元数据合并和清理
- 向量记录构建
- ID生成和验证

**使用示例：**
```python
from app.utils.data_utils import RecordBuilder, TextProcessor, IDGenerator

# 构建向量记录
record = RecordBuilder.build_vector_record(
    content="测试文本",
    vector=[0.1, 0.2, 0.3],
    metadata={"type": "test"},
    collection="documents"
)

# 文本分块
chunks = TextProcessor.chunk_text("长文本内容", chunk_size=100, chunk_overlap=20)

# 生成文档ID
doc_id = IDGenerator.generate_doc_id()
```

## 设计原则

### 1. 单一职责原则
每个工具类都专注于特定的功能域，如验证、格式化、日志记录等。

### 2. 可复用性
所有工具函数都设计为可在多个地方复用，避免代码重复。

### 3. 错误处理
提供完善的错误处理机制，包括类型转换错误、验证错误等。

### 4. 类型安全
使用Python类型提示，提高代码的可读性和IDE支持。

### 5. 扩展性
设计时考虑了未来的扩展需求，便于添加新的工具函数。

## 集成指南

### 在路由层使用工具

```python
from app.utils.validation import validate_search_parameters
from app.utils.filter_utils import build_combined_filter_expression, get_search_standard_fields
from app.utils.formatting import SearchResultFormatter
from app.utils.logging_utils import track_time, log_request_details
from app.utils.response_utils import SearchResponseBuilder

@router.post("/search")
async def search(request: SearchRequest, raw_request: Request):
    # 记录请求详情
    log_request_details(request, raw_request)
    
    # 验证参数
    top_k, _ = validate_search_parameters(request.top_k)
    
    # 构建过滤表达式
    filter_expr = build_combined_filter_expression(request, get_search_standard_fields())
    
    # 时间追踪
    tracker = track_time("向量搜索")
    
    try:
        # 执行搜索逻辑
        tracker.checkpoint("向量生成")
        # ... 向量生成代码 ...
        
        tracker.checkpoint("数据库查询")
        # ... 数据库查询代码 ...
        
        tracker.checkpoint("结果格式化")
        formatted_results = SearchResultFormatter.format_search_results(raw_results)
        
        time_details = tracker.finish()
        
        # 构建响应
        return SearchResponseBuilder.search_response(
            query=request.text,
            results=formatted_results,
            search_time=time_details.get('total', 0),
            time_details=time_details
        )
        
    except Exception as e:
        return handle_exception_to_response(e, "搜索")
```

## 性能考虑

1. **缓存机制**: 某些验证结果可以缓存以提高性能
2. **批量处理**: 列表处理器支持高效的批量操作
3. **内存管理**: 大文本分块时注意内存使用
4. **异步支持**: 部分工具支持异步操作

## 测试建议

每个工具类都应该有对应的单元测试：

```python
# tests/test_utils/test_validation.py
def test_validate_top_k():
    assert validate_top_k("5") == 5
    assert validate_top_k(10) == 10
    
    with pytest.raises(ValidationError):
        validate_top_k("invalid")
```

## 扩展指南

当需要添加新的工具功能时：

1. **确定功能分类**: 判断应该放在哪个现有模块中
2. **创建新模块**: 如果功能不适合现有模块，创建新的工具模块
3. **遵循命名约定**: 使用清晰的类名和函数名
4. **添加类型提示**: 确保所有函数都有完整的类型提示
5. **编写文档**: 为新功能添加详细的文档字符串
6. **添加测试**: 为新功能编写对应的单元测试

## 维护注意事项

1. **版本兼容性**: 修改工具函数时要考虑向后兼容性
2. **依赖管理**: 避免在工具层引入重量级依赖
3. **文档更新**: 修改功能时及时更新文档
4. **性能监控**: 定期检查工具函数的性能表现 