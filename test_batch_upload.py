import asyncio
import json
import httpx

async def test_batch_upload():
    """测试批量上传接口"""
    
    base_url = "http://copilot.csvw.com/rag_service"
    
    # 准备测试数据
    test_data = {
        "items": [
            {
                "text": "这是第一条测试文本，用于验证批量上传功能。",
                "metadata": {
                    "category": "test",
                    "priority": "high",
                    "author": "user1"
                },
                # "id": "test_001"
            },
            {
                "text": "第二条文本内容，具有不同的元数据。",
                "metadata": {
                    "category": "demo",
                    "priority": "medium",
                    "author": "user2"
                },
                # "id": "test_002"
            },
            {
                "text": "第三条文本，展示了不同类别的内容处理。",
                "metadata": {
                    "category": "example",
                    "priority": "low",
                    "author": "user1"
                },
                # "id": "test_003"
            },
            {
                "text": "第四条文本，具有更复杂的元数据结构。",
                "metadata": {
                    "category": "complex",
                    "priority": "high",
                    "author": "user3",
                    "tags": ["important", "urgent"],
                    "score": 95.5
                },
                # "id": "test_004"
            },
            {
                "text": "第五条文本，用于测试批量处理的性能。",
                "metadata": {
                    "category": "performance",
                    "priority": "medium",
                    "author": "user2",
                    "benchmark": True
                },
                # "id": "test_005"
            }
        ],
        "collection": "test_batch_collection_V1",
        "database": "test_db_V1",
        "global_metadata": {
            "upload_time": "2024-01-15",
            "version": "1.0",
            "environment": "test"
        },
        "encrypt": False,
        "embedding_type": "huggingface",
        "batch_size": 3  # 设置为3，这样5个文本会分为2个批次处理
    }
    
    print("=== 测试批量上传接口 ===")
    print(f"测试数据包含 {len(test_data['items'])} 个文本项")
    print(f"批次大小: {test_data['batch_size']}")
    print(f"集合名称: {test_data['collection']}")
    print(f"数据库: {test_data['database']}")
    print(f"全局元数据: {test_data['global_metadata']}")
    print()
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            print("发送批量上传请求...")
            response = await client.post(
                f"{base_url}/api/v1/upload_texts_batch",
                json=test_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 批量上传成功！")
                print(f"文档ID: {result['doc_id']}")
                print(f"处理的文本数量: {result['text_count']}")
                print(f"批次数量: {result['batch_count']}")
                print(f"实际批次大小: {result['batch_size']}")
                print(f"向量维度: {result['vector_dimension']}")
                print(f"总处理时间: {result['processing_time']}")
                print(f"平均每文本时间: {result['avg_time_per_text']}")
                
                # 测试搜索功能
                print("\n=== 测试搜索功能 ===")
                search_data = {
                    "text": "测试文本",
                    "top_k": 3,
                    "collection": test_data['collection'],
                    "database": test_data['database']
                }
                
                print("搜索相关文本...")
                search_response = await client.post(
                    f"{base_url}/api/v1/search",
                    json=search_data
                )
                
                if search_response.status_code == 200:
                    search_result = search_response.json()
                    print(f"✅ 搜索成功！找到 {search_result['total_results']} 条结果")
                    
                    for i, result in enumerate(search_result['results'][:3]):
                        print(f"\n结果 {i+1}:")
                        print(f"  相似度: {result['similarity']:.4f}")
                        print(f"  内容: {result['content'][:50]}...")
                        print(f"  元数据: {result.get('metadata', {})}")
                else:
                    print(f"❌ 搜索失败: {search_response.status_code}")
                    print(search_response.text)
                    
            else:
                print(f"❌ 批量上传失败: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"❌ 请求出错: {e}")

if __name__ == "__main__":
    asyncio.run(test_batch_upload()) 