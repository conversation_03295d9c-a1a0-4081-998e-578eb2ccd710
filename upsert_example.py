#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Upsert接口调用示例 - 自动向量生成模式

展示如何使用改进后的upsert接口，无需手动传入向量
系统会自动根据content或text字段生成向量
"""

import requests
import json
from typing import List, Dict, Any
import time

# 配置API基础URL
API_BASE_URL = "http://localhost:8000/api/v1"
UPSERT_URL = f"{API_BASE_URL}/upsert"

def call_upsert_api(data: Dict[str, Any]) -> Dict[str, Any]:
    """调用upsert接口的通用函数"""
    try:
        print(f"[请求] 调用upsert接口...")
        print(f"[请求] 数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            UPSERT_URL,
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5分钟超时
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"[成功] 状态码: {response.status_code}")
            print(f"[成功] 返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result
        else:
            print(f"[错误] 状态码: {response.status_code}")
            print(f"[错误] 错误信息: {response.text}")
            return {"error": response.text, "status_code": response.status_code}
            
    except requests.exceptions.Timeout:
        print("[错误] 请求超时")
        return {"error": "请求超时"}
    except requests.exceptions.ConnectionError:
        print("[错误] 连接错误，请检查服务是否启动")
        return {"error": "连接错误"}
    except Exception as e:
        print(f"[错误] 未知错误: {e}")
        return {"error": str(e)}

def example_1_basic_upsert():
    """示例1: 基本的单条记录更新"""
    print("\n" + "="*50)
    print("示例1: 基本的单条记录更新（自动生成向量）")
    print("="*50)
    
    data = {
        "records": [
            {
                "id": "doc_001",  # 必须包含主键
                "content": "这是一个关于人工智能技术发展的文档，介绍了最新的机器学习算法和应用场景。",
                "metadata": {
                    "category": "technology",
                    "author": "张三",
                    "created_date": "2024-01-15",
                    "version": 2
                }
            }
        ],
        "collection": "documents",
        "database": "test_db",  # 可选，不指定则使用默认数据库
        "auto_flush": True
    }
    
    result = call_upsert_api(data)
    return result

def example_2_batch_upsert():
    """示例2: 批量更新多条记录"""
    print("\n" + "="*50)
    print("示例2: 批量更新多条记录（自动生成向量）")
    print("="*50)
    
    data = {
        "records": [
            {
                "id": "article_001",
                "content": "深度学习是机器学习的一个重要分支，它模拟人脑神经网络的结构和功能。",
                "metadata": {
                    "category": "AI",
                    "tags": ["深度学习", "神经网络"],
                    "difficulty": "intermediate"
                }
            },
            {
                "id": "article_002", 
                "text": "自然语言处理技术正在快速发展，GPT模型展现出了强大的文本生成能力。",  # 支持text字段
                "metadata": {
                    "category": "NLP",
                    "tags": ["自然语言处理", "GPT"],
                    "difficulty": "advanced"
                }
            },
            {
                "id": "article_003",
                "content": "计算机视觉技术在自动驾驶、医疗影像分析等领域有广泛应用。",
                "metadata": {
                    "category": "CV",
                    "tags": ["计算机视觉", "自动驾驶"],
                    "difficulty": "intermediate"
                }
            }
        ],
        "collection": "articles",
        "auto_flush": True
    }
    
    result = call_upsert_api(data)
    return result

def example_3_with_embedding_type():
    """示例3: 指定向量模型类型"""
    print("\n" + "="*50)
    print("示例3: 指定向量模型类型")
    print("="*50)
    
    data = {
        "records": [
            {
                "id": "news_001",
                "content": "今日科技新闻：某公司发布了新一代AI芯片，性能提升50%。",
                "metadata": {
                    "category": "news",
                    "source": "科技日报",
                    "publish_date": "2024-01-15"
                }
            }
        ],
        "collection": "news",
        "embedding_type": "huggingface",  # 指定使用huggingface模型
        "auto_flush": True
    }
    
    result = call_upsert_api(data)
    return result

def example_4_with_encryption():
    """示例4: 使用内容加密"""
    print("\n" + "="*50)
    print("示例4: 使用内容加密（自动生成向量）")
    print("="*50)
    
    data = {
        "records": [
            {
                "id": "sensitive_001",
                "content": "这是一份包含敏感信息的机密文档，需要加密存储。",
                "metadata": {
                    "classification": "confidential",
                    "department": "research",
                    "access_level": "restricted"
                }
            }
        ],
        "collection": "sensitive_docs",
        "encrypt": True,  # 启用内容加密
        "auto_flush": True
    }
    
    result = call_upsert_api(data)
    return result

def example_5_mixed_mode():
    """示例5: 混合模式（部分自动生成，部分手动提供向量）"""
    print("\n" + "="*50)
    print("示例5: 混合模式（自动+手动向量）")
    print("="*50)
    
    data = {
        "records": [
            {
                "id": "mixed_001",
                "content": "这条记录会自动生成向量",  # 自动生成向量
                "metadata": {"type": "auto_generated"}
            },
            {
                "id": "mixed_002",
                "vector": [0.1, 0.2, 0.3, 0.4, 0.5] * 153 + [0.1, 0.2, 0.3],  # 手动提供向量（768维）
                "content": "这条记录使用手动提供的向量",
                "metadata": {"type": "manual_vector"}
            }
        ],
        "collection": "mixed_docs",
        "auto_flush": True
    }
    
    result = call_upsert_api(data)
    return result

def example_6_dynamic_fields():
    """示例6: 使用动态字段"""
    print("\n" + "="*50)
    print("示例6: 使用动态字段（自动生成向量）")
    print("="*50)
    
    data = {
        "records": [
            {
                "id": "product_001",
                "content": "iPhone 15 Pro Max 是苹果公司最新推出的旗舰手机，配备A17 Pro芯片。",
                "metadata": {
                    "category": "electronics",
                    "brand": "Apple"
                },
                # 动态字段
                "product_name": "iPhone 15 Pro Max",
                "price": 9999.00,
                "in_stock": True,
                "rating": 4.8,
                "release_year": 2023
            },
            {
                "id": "product_002", 
                "text": "特斯拉Model Y是一款纯电动SUV，续航里程可达500公里。",
                "metadata": {
                    "category": "automotive",
                    "brand": "Tesla"
                },
                # 动态字段
                "product_name": "Model Y",
                "price": 299999.00,
                "in_stock": False,
                "rating": 4.6,
                "release_year": 2022
            }
        ],
        "collection": "products",
        "auto_flush": True
    }
    
    result = call_upsert_api(data)
    return result

def example_7_error_handling():
    """示例7: 错误处理演示"""
    print("\n" + "="*50)
    print("示例7: 错误处理演示")
    print("="*50)
    
    # 故意制造错误：缺少content和vector字段
    data = {
        "records": [
            {
                "id": "error_001",
                # 缺少content/text和vector字段
                "metadata": {"type": "error_demo"}
            }
        ],
        "collection": "error_docs"
    }
    
    result = call_upsert_api(data)
    return result

def run_all_examples():
    """运行所有示例"""
    print("🚀 开始运行upsert接口调用示例")
    print("📝 注意：这些示例展示了如何使用自动向量生成功能")
    
    examples = [
        example_1_basic_upsert,
        example_2_batch_upsert,
        example_3_with_embedding_type,
        example_4_with_encryption,
        example_5_mixed_mode,
        example_6_dynamic_fields,
        example_7_error_handling
    ]
    
    results = []
    
    for i, example_func in enumerate(examples, 1):
        try:
            print(f"\n🔄 正在运行示例 {i}...")
            result = example_func()
            results.append(result)
            
            # 检查是否成功
            if "error" not in result:
                print(f"✅ 示例 {i} 执行成功")
            else:
                print(f"❌ 示例 {i} 执行失败: {result.get('error', '未知错误')}")
                
            # 间隔一下，避免请求过快
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 示例 {i} 执行异常: {e}")
            results.append({"error": str(e)})
    
    # 打印总结
    print("\n" + "="*50)
    print("📊 执行总结")
    print("="*50)
    
    success_count = sum(1 for r in results if "error" not in r)
    total_count = len(results)
    
    print(f"总示例数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    return results

if __name__ == "__main__":
    print("📚 Upsert接口调用示例")
    print("🎯 本示例展示如何使用自动向量生成功能")
    print("🔧 确保RAG服务已启动在 http://localhost:8000")
    
    # 选择运行方式
    print("\n请选择运行方式:")
    print("1. 运行所有示例")
    print("2. 运行单个示例")
    print("3. 查看示例说明")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        run_all_examples()
    elif choice == "2":
        print("\n可用示例:")
        print("1. 基本单条记录更新")
        print("2. 批量更新")
        print("3. 指定向量模型")
        print("4. 内容加密")
        print("5. 混合模式")
        print("6. 动态字段")
        print("7. 错误处理")
        
        example_choice = input("\n请选择示例 (1-7): ").strip()
        
        examples_map = {
            "1": example_1_basic_upsert,
            "2": example_2_batch_upsert,
            "3": example_3_with_embedding_type,
            "4": example_4_with_encryption,
            "5": example_5_mixed_mode,
            "6": example_6_dynamic_fields,
            "7": example_7_error_handling
        }
        
        if example_choice in examples_map:
            examples_map[example_choice]()
        else:
            print("❌ 无效选择")
    elif choice == "3":
        print("""
📖 示例说明:

1. 基本单条记录更新
   - 演示最基本的upsert用法
   - 只需要提供id和content字段
   - 系统自动生成向量

2. 批量更新
   - 演示如何批量更新多条记录
   - 支持content和text字段

3. 指定向量模型
   - 演示如何使用embedding_type参数
   - 可选择不同的向量生成模型

4. 内容加密
   - 演示如何使用encrypt参数
   - 内容会被加密存储

5. 混合模式  
   - 演示同时使用自动和手动向量
   - 灵活性最高的使用方式

6. 动态字段
   - 演示如何使用自定义字段
   - 适合结构化数据存储

7. 错误处理
   - 演示错误情况和处理方式
   - 帮助理解接口要求

🔗 接口特点:
- ✅ 自动向量生成（主要特性）
- ✅ 支持手动向量（向后兼容）
- ✅ 混合模式（灵活性）
- ✅ 内容加密（安全性）
- ✅ 多种向量模型（可选性）
- ✅ 动态字段（扩展性）
        """)
    else:
        print("❌ 无效选择") 