#!/usr/bin/env python3
"""
测试自定义ID功能的最终版本
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://copilot.csvw.com/rag_service/api/v1"

def test_auto_id_with_dynamic_fields():
    """测试自增ID + 动态字段模式"""
    print("=== 测试自增ID + 动态字段模式 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "这是一辆红色的轿车，配备了先进的安全系统",
            "这是一辆蓝色的SUV，适合家庭出行"
        ],
        "collection": "test_auto_id_dynamic",
        "database": "test_db",
        "car_type": "passenger",      # 动态字段
        "model_year": 2024,           # 动态字段
        "brand": "TestBrand",         # 动态字段
        "metadata": {
            "category": "automotive",
            "source": "test_data"
        }
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"成功: {result}")
        return result.get('doc_id')
    else:
        print(f"失败: {response.json()}")
        return None

def test_custom_id_without_dynamic_fields():
    """测试自定义ID模式（不支持动态字段）"""
    print("\n=== 测试自定义ID模式 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "这是第一个自定义ID文档",
            "这是第二个自定义ID文档",
            "这是第三个自定义ID文档"
        ],
        "ids": [10001, 10002, 10003],  # 自定义ID
        "collection": "test_custom_id_only",
        "database": "test_db",
        "metadata": {
            "category": "custom_id_test",
            "source": "manual"
        }
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"成功: {result}")
        return result.get('doc_id')
    else:
        print(f"失败: {response.json()}")
        return None

def test_custom_id_with_dynamic_fields_should_warn():
    """测试自定义ID + 动态字段（应该给出警告）"""
    print("\n=== 测试自定义ID + 动态字段（应该警告） ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "这是一个测试文档"
        ],
        "ids": [20001],  # 自定义ID
        "collection": "test_custom_id_with_dynamic",
        "database": "test_db",
        "car_type": "sedan",  # 动态字段（应该被忽略）
        "model_year": 2024,   # 动态字段（应该被忽略）
        "metadata": {
            "category": "test",
            "source": "manual"
        }
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"成功: {result}")
        print("注意：动态字段应该被忽略并给出警告")
        return result.get('doc_id')
    else:
        print(f"失败: {response.json()}")
        return None

def test_search_auto_id_collection():
    """测试搜索自增ID集合"""
    print("\n=== 测试搜索自增ID集合 ===")
    
    time.sleep(2)  # 等待索引
    
    url = f"{BASE_URL}/search"
    data = {
        "text": "红色轿车",
        "collection": "test_auto_id_dynamic",
        "database": "test_db",
        "top_k": 5
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"查询: {result.get('query')}")
        print(f"结果数量: {result.get('total_results')}")
        
        for i, res in enumerate(result.get('results', []), 1):
            print(f"\n结果 {i}:")
            print(f"  内容: {res.get('content')}")
            print(f"  相似度: {res.get('similarity'):.4f}")
            print(f"  ID: {res.get('id')}")
            print(f"  car_type: {res.get('car_type', '未找到')}")
            print(f"  model_year: {res.get('model_year', '未找到')}")
            print(f"  brand: {res.get('brand', '未找到')}")
    else:
        print(f"搜索失败: {response.json()}")

def test_search_custom_id_collection():
    """测试搜索自定义ID集合"""
    print("\n=== 测试搜索自定义ID集合 ===")
    
    time.sleep(2)  # 等待索引
    
    url = f"{BASE_URL}/search"
    data = {
        "text": "自定义ID文档",
        "collection": "test_custom_id_only",
        "database": "test_db",
        "top_k": 5
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"查询: {result.get('query')}")
        print(f"结果数量: {result.get('total_results')}")
        
        for i, res in enumerate(result.get('results', []), 1):
            print(f"\n结果 {i}:")
            print(f"  内容: {res.get('content')}")
            print(f"  相似度: {res.get('similarity'):.4f}")
            print(f"  ID: {res.get('id')} (应该是自定义ID: 10001, 10002, 10003)")
            # 自定义ID模式下不应该有动态字段
            print(f"  动态字段: 无（自定义ID模式不支持动态字段）")
    else:
        print(f"搜索失败: {response.json()}")

def test_id_validation():
    """测试ID验证"""
    print("\n=== 测试ID验证 ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": ["文本1", "文本2", "文本3"],
        "ids": [1, 2],  # 长度不匹配
        "collection": "test_validation"
    }
    
    response = requests.post(url, json=data)
    print(f"ID长度不匹配测试 - 状态码: {response.status_code}")
    if response.status_code == 400:
        print(f"✓ 正确返回400错误: {response.json()}")
    else:
        print(f"✗ 未正确验证: {response.json()}")

def test_string_ids():
    """测试字符串ID"""
    print("\n=== 测试字符串ID ===")
    
    url = f"{BASE_URL}/upload_texts"
    data = {
        "texts": [
            "文档A的内容",
            "文档B的内容"
        ],
        "ids": ["doc_a", "doc_b"],  # 字符串ID
        "collection": "test_string_ids",
        "database": "test_db",
        "metadata": {
            "category": "string_id_test"
        }
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"成功: {result}")
    else:
        print(f"失败: {response.json()}")

if __name__ == "__main__":
    print("开始测试自定义ID功能...\n")
    
    try:
        # 测试自增ID + 动态字段模式
        test_auto_id_with_dynamic_fields()
        
        # 测试自定义ID模式
        test_custom_id_without_dynamic_fields()
        
        # 测试自定义ID + 动态字段（应该警告）
        test_custom_id_with_dynamic_fields_should_warn()
        
        # 测试搜索功能
        test_search_auto_id_collection()
        test_search_custom_id_collection()
        
        # 测试验证功能
        test_id_validation()
        
        # 测试字符串ID
        test_string_ids()
        
        print("\n所有测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到API服务器")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
