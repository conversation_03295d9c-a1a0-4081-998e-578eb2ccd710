# Milvus 向量数据库迁移工具使用说明

## 问题描述

在使用原始的 `fixed_migration.py` 迁移工具时，当数据量较大时会遇到以下错误：

```
MilvusException: (code=65535, message=invalid max query result window, (offset+limit) should be in range [1, 16384], but got 32500)
```

这是因为 Milvus 对查询操作有限制：`offset + limit` 的值不能超过 16,384。

## 解决方案

我们提供了两个解决方案：

### 方案一：使用迭代器版本（推荐）

**文件：** `fixed_migration_iterator.py`

**特点：**
- 使用 PyMilvus 的 `query_iterator` 方法
- 完全避免 16,384 限制
- 能够处理任意大小的数据集
- 更高效的内存使用

**使用方法：**

1. 确保安装了 PyMilvus：
   ```bash
   pip install pymilvus
   ```

2. 运行迁移工具：
   ```bash
   python fixed_migration_iterator.py
   ```

3. 选择迁移选项：
   - 选择 1：迁移所有集合
   - 选择 2：迁移指定集合

### 方案二：使用修复版本

**文件：** `fixed_migration.py`（已修复）

**特点：**
- 智能检测是否会超过 16,384 限制
- 当接近限制时自动停止并提供建议
- 使用更安全的批次大小
- 向后兼容原有功能

**使用方法：**

```bash
python fixed_migration.py
```

## 配置说明

两个工具都使用相同的配置文件 `migration_config.py`：

```python
# 源数据库配置
SOURCE_CONFIG = {
    "uri": "你的源数据库URI",
    "token": "你的源数据库Token",
    "database": "你的源数据库名称"
}

# 目标数据库配置
TARGET_CONFIG = {
    "uri": "你的目标数据库URI", 
    "token": "你的目标数据库Token",
    "database": "你的目标数据库名称"
}

# 迁移配置
MIGRATION_CONFIG = {
    "batch_size": 1000,  # 批次大小
    "verify_after_migration": True  # 迁移后验证
}
```

## 技术原理

### 16,384 限制的原因

Milvus 为了防止内存溢出和性能问题，对单次查询的结果数量进行了限制。当使用 `offset` 和 `limit` 参数时：

- Milvus 需要先获取 `offset + limit` 条记录
- 然后跳过前 `offset` 条记录
- 最后返回 `limit` 条记录

这意味着即使你只想要最后的 100 条记录，如果 `offset` 很大，Milvus 仍需要处理大量数据。

### 迭代器解决方案

PyMilvus 的 `query_iterator` 方法：

1. **分批处理**：自动将大查询分解为多个小批次
2. **状态保持**：迭代器内部维护查询状态，确保不重复不遗漏
3. **内存优化**：每次只加载一个批次的数据到内存
4. **无限制**：不受 16,384 限制影响

### 代码示例

```python
from pymilvus import Collection

# 创建集合对象
collection = Collection("your_collection", using="your_connection")

# 创建查询迭代器
iterator = collection.query_iterator(
    batch_size=1000,      # 每批处理1000条记录
    limit=None,           # 获取所有数据
    expr="",              # 无过滤条件
    output_fields=["*"]   # 获取所有字段
)

# 迭代处理数据
while True:
    results = iterator.next()
    if not results:
        break
    
    # 处理这批数据
    process_batch(results)

# 关闭迭代器
iterator.close()
```

## 性能对比

| 方法 | 数据量限制 | 内存使用 | 处理速度 | 稳定性 |
|------|------------|----------|----------|--------|
| 传统 offset/limit | 16,384 | 高 | 慢 | 差 |
| 迭代器方式 | 无限制 | 低 | 快 | 好 |

## 常见问题

### Q1: 迁移过程中断了怎么办？

A: 迭代器版本支持断点续传。重新运行工具时，它会检查目标数据库中已有的数据，避免重复迁移。

### Q2: 如何验证迁移结果？

A: 工具会自动验证迁移结果，比较源数据库和目标数据库的记录数。如果自动验证失败，会提示手动确认。

### Q3: 迁移速度慢怎么办？

A: 可以调整 `batch_size` 参数：
- 增大 `batch_size`：提高速度，但增加内存使用
- 减小 `batch_size`：降低内存使用，但可能影响速度

### Q4: 遇到连接错误怎么办？

A: 检查以下配置：
1. 数据库 URI 是否正确
2. Token 是否有效
3. 网络连接是否正常
4. 数据库是否正在运行

## 日志说明

工具会生成详细的日志文件：
- `migration_iterator.log`：迭代器版本的日志
- `migration.log`：修复版本的日志

日志包含：
- 连接状态
- 迁移进度
- 错误信息
- 性能统计

## 建议

1. **优先使用迭代器版本**：能够处理任意大小的数据集
2. **测试小数据集**：先用小数据集测试配置是否正确
3. **监控资源使用**：注意内存和网络使用情况
4. **备份重要数据**：迁移前确保有数据备份
5. **分批迁移**：对于超大数据集，可以分多次迁移不同的集合

## 技术支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. 数据库连接配置
3. PyMilvus 版本兼容性
4. 网络连接状态

更多技术细节请参考：
- [Milvus 官方文档](https://milvus.io/docs)
- [PyMilvus 迭代器文档](https://milvus.io/docs/with-iterators.md)
