"""
搜索操作Repository

负责：
- 向量搜索（单集合和多集合）
- 语义搜索
- 多数据库搜索
- 搜索结果处理
- 搜索性能优化
"""

import asyncio
from typing import List, Dict, Any, Optional, Union
from app.core.vectordb import VectorDatabase
from app.utils.formatting import SearchResultFormatter
from app.utils.logging_utils import TimeTracker


class SearchRepository:
    """搜索操作Repository"""
    
    def __init__(self, db: VectorDatabase):
        """
        初始化搜索操作Repository
        
        Args:
            db: 数据库连接实例
        """
        self.db = db
        self.formatter = SearchResultFormatter()
        self.time_tracker = TimeTracker()
    
    async def search_vectors(
        self,
        collection_name: str,
        query_vector: List[float],
        top_k: int = 10,
        filter_expr: str = ""
    ) -> List[Dict[str, Any]]:
        """
        在指定集合中搜索向量
        
        Args:
            collection_name: 集合名称，可以是'all'表示搜索所有集合
            query_vector: 查询向量
            top_k: 返回结果数量
            filter_expr: 筛选表达式
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
            
        Raises:
            Exception: 搜索失败
        """
        self.time_tracker.start()
        
        print(f"[搜索] 向量搜索 - 集合: '{collection_name}', top_k: {top_k}")
        if filter_expr:
            print(f"[搜索] 筛选条件: {filter_expr}")
        
        try:
            if hasattr(self.db, 'search_vectors'):
                search_method = getattr(self.db, 'search_vectors')
                results = await search_method(collection_name, query_vector, top_k, filter_expr)
                
                search_time = self.time_tracker.get_time()
                print(f"[搜索] 搜索完成，返回 {len(results)} 条结果，耗时: {search_time:.3f}秒")
                
                return results
            else:
                raise Exception("数据库不支持向量搜索操作")
        except Exception as e:
            print(f"[错误] 向量搜索失败: {e}")
            raise Exception(f"向量搜索失败: {str(e)}")
    
    async def search_all_collections(
        self,
        query_vector: List[float],
        top_k: int = 10,
        filter_expr: str = ""
    ) -> List[Dict[str, Any]]:
        """
        搜索所有集合
        
        Args:
            query_vector: 查询向量
            top_k: 返回结果数量
            filter_expr: 筛选表达式
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
            
        Raises:
            Exception: 搜索失败
        """
        print(f"[搜索] 搜索所有集合，top_k: {top_k}")
        
        try:
            return await self.search_vectors('all', query_vector, top_k, filter_expr)
        except Exception as e:
            print(f"[错误] 搜索所有集合失败: {e}")
            raise Exception(f"搜索所有集合失败: {str(e)}")
    
    async def search_multiple_collections(
        self,
        collection_names: List[str],
        query_vector: List[float],
        top_k: int = 10,
        filter_expr: str = ""
    ) -> List[Dict[str, Any]]:
        """
        搜索多个指定集合
        
        Args:
            collection_names: 集合名称列表
            query_vector: 查询向量
            top_k: 每个集合返回的结果数量
            filter_expr: 筛选表达式
            
        Returns:
            List[Dict[str, Any]]: 合并后的搜索结果列表
            
        Raises:
            Exception: 搜索失败
        """
        print(f"[搜索] 搜索多个集合: {collection_names}，每个集合top_k: {top_k}")
        
        try:
            all_results = []
            
            # 并发搜索所有集合
            async def search_collection(collection_name: str) -> List[Dict[str, Any]]:
                try:
                    results = await self.search_vectors(collection_name, query_vector, top_k, filter_expr)
                    # 为每个结果添加集合信息
                    for result in results:
                        result['collection'] = collection_name
                    return results
                except Exception as e:
                    print(f"[警告] 搜索集合 '{collection_name}' 失败: {e}")
                    return []
            
            # 创建并发任务
            tasks = [search_collection(collection) for collection in collection_names]
            results_list = await asyncio.gather(*tasks)
            
            # 合并所有结果
            for results in results_list:
                all_results.extend(results)
            
            # 按相似度排序
            all_results.sort(key=lambda x: x.get('distance', 0), reverse=True)
            
            print(f"[搜索] 多集合搜索完成，总共返回 {len(all_results)} 条结果")
            return all_results
            
        except Exception as e:
            print(f"[错误] 多集合搜索失败: {e}")
            raise Exception(f"多集合搜索失败: {str(e)}")
    
    async def format_search_results(
        self,
        results: List[Dict[str, Any]],
        auto_decrypt: bool = True
    ) -> List[Dict[str, Any]]:
        """
        格式化搜索结果
        
        Args:
            results: 原始搜索结果
            auto_decrypt: 是否自动解密加密内容
            
        Returns:
            List[Dict[str, Any]]: 格式化后的结果
        """
        print(f"[格式化] 开始格式化 {len(results)} 条搜索结果")
        
        try:
            formatted_results = []
            
            for i, result in enumerate(results):
                try:
                    formatted_result = self.formatter.format_search_result(result, auto_decrypt)
                    formatted_results.append(formatted_result)
                except Exception as format_error:
                    print(f"[警告] 格式化结果 {i+1} 失败: {format_error}")
                    # 返回原始结果
                    formatted_results.append(result)
            
            print(f"[格式化] 结果格式化完成")
            return formatted_results
            
        except Exception as e:
            print(f"[错误] 格式化搜索结果失败: {e}")
            # 返回原始结果
            return results
    
    def calculate_similarity_from_distance(self, distance: float, metric_type: str = "cosine") -> float:
        """
        从距离计算相似度
        
        Args:
            distance: 距离值
            metric_type: 度量类型
            
        Returns:
            float: 相似度值 (0-1)
        """
        if metric_type.lower() == "cosine":
            # 余弦相似度：distance已经是相似度值
            return distance
        elif metric_type.lower() == "l2":
            # L2距离：需要转换为相似度
            return 1.0 / (1.0 + distance)
        elif metric_type.lower() == "ip":
            # 内积：可能需要归一化
            return max(0.0, min(1.0, distance))
        else:
            # 默认直接返回距离值
            return distance
    
    async def search_with_pagination(
        self,
        collection_name: str,
        query_vector: List[float],
        page: int = 1,
        page_size: int = 10,
        filter_expr: str = ""
    ) -> Dict[str, Any]:
        """
        分页搜索
        
        Args:
            collection_name: 集合名称
            query_vector: 查询向量
            page: 页码（从1开始）
            page_size: 每页大小
            filter_expr: 筛选表达式
            
        Returns:
            Dict[str, Any]: 包含分页信息的搜索结果
            
        Raises:
            Exception: 搜索失败
        """
        print(f"[分页搜索] 集合: '{collection_name}', 页码: {page}, 每页: {page_size}")
        
        try:
            # 计算需要获取的总结果数
            total_needed = page * page_size
            
            # 执行搜索
            all_results = await self.search_vectors(collection_name, query_vector, total_needed, filter_expr)
            
            # 计算分页信息
            total_results = len(all_results)
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            
            # 获取当前页的结果
            page_results = all_results[start_index:end_index]
            
            # 计算分页元数据
            total_pages = (total_results + page_size - 1) // page_size
            has_next = page < total_pages
            has_prev = page > 1
            
            return {
                "results": page_results,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_results": total_results,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev,
                    "results_in_page": len(page_results)
                }
            }
            
        except Exception as e:
            print(f"[错误] 分页搜索失败: {e}")
            raise Exception(f"分页搜索失败: {str(e)}")
    
    async def search_with_rerank(
        self,
        collection_name: str,
        query_vector: List[float],
        top_k: int = 10,
        rerank_factor: float = 2.0,
        filter_expr: str = ""
    ) -> List[Dict[str, Any]]:
        """
        带重新排序的搜索
        
        Args:
            collection_name: 集合名称
            query_vector: 查询向量
            top_k: 最终返回结果数量
            rerank_factor: 重排序因子（先获取top_k * rerank_factor条结果，再重新排序）
            filter_expr: 筛选表达式
            
        Returns:
            List[Dict[str, Any]]: 重新排序后的搜索结果
            
        Raises:
            Exception: 搜索失败
        """
        print(f"[重排序搜索] 集合: '{collection_name}', top_k: {top_k}, 重排序因子: {rerank_factor}")
        
        try:
            # 先获取更多的候选结果
            candidate_count = int(top_k * rerank_factor)
            candidate_results = await self.search_vectors(collection_name, query_vector, candidate_count, filter_expr)
            
            # 这里可以添加更复杂的重排序逻辑
            # 例如基于内容质量、时间戳、用户偏好等进行重新评分
            
            # 简单的重排序：按现有相似度排序并截取
            reranked_results = sorted(
                candidate_results,
                key=lambda x: x.get('distance', 0),
                reverse=True
            )[:top_k]
            
            print(f"[重排序搜索] 从 {len(candidate_results)} 个候选结果中选出 {len(reranked_results)} 个最终结果")
            return reranked_results
            
        except Exception as e:
            print(f"[错误] 重排序搜索失败: {e}")
            raise Exception(f"重排序搜索失败: {str(e)}")
    
    async def search_with_filters(
        self,
        collection_name: str,
        query_vector: List[float],
        top_k: int = 10,
        metadata_filters: Optional[Dict[str, Any]] = None,
        field_filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        带多种筛选条件的搜索
        
        Args:
            collection_name: 集合名称
            query_vector: 查询向量
            top_k: 返回结果数量
            metadata_filters: metadata字段筛选条件
            field_filters: 直接字段筛选条件
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
            
        Raises:
            Exception: 搜索失败
        """
        from app.utils.filter_utils import FilterExpressionBuilder
        
        filter_builder = FilterExpressionBuilder()
        
        # 构建筛选表达式
        filter_expr = filter_builder.build_filter_expression(
            metadata_filters=metadata_filters,
            field_filters=field_filters
        )
        
        print(f"[筛选搜索] 使用筛选条件: {filter_expr}")
        
        return await self.search_vectors(collection_name, query_vector, top_k, filter_expr)
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """
        获取搜索统计信息
        
        Returns:
            Dict[str, Any]: 搜索统计信息
        """
        return {
            "total_search_time": self.time_tracker.get_total_time(),
            "search_count": self.time_tracker.get_checkpoint_count(),
            "average_search_time": self.time_tracker.get_average_time(),
            "time_breakdown": self.time_tracker.get_time_breakdown()
        } 