import pytest
from pathlib import Path
from app.core.knowledge_processing import (
    DocumentChunk,
    PDFParser,
    HTMLParser,
    WholeDocumentStrategy,
    FixedSizeChunkStrategy
)

@pytest.fixture
def sample_pdf(tmp_path):
    pdf_path = tmp_path / "test.pdf"
    # 创建简单PDF测试文件
    import fitz
    doc = fitz.open()
    page = doc.new_page()
    page.insert_text((50, 50), "Test PDF Content")
    doc.save(pdf_path)
    doc.close()
    return str(pdf_path)

@pytest.fixture
def sample_html(tmp_path):
    html_path = tmp_path / "test.html"
    html_content = """
    <html>
        <body>
            <h1>Test HTML</h1>
            <p>Paragraph 1</p>
            <img src="test.png">
            <script>console.log('test')</script>
        </body>
    </html>
    """
    html_path.write_text(html_content)
    return str(html_path)

class TestKnowledgeProcessing:
    def test_document_chunk_conversion(self):
        chunk = DocumentChunk(
            document_id="123",
            content="test content",
            metadata={"source": "test.txt"},
            image_paths=["img1.png"]
        )
        vector_record = chunk.to_vector_record([0.1, 0.2])
        
        assert vector_record["vector"] == [0.1, 0.2]
        assert vector_record["attributes"]["doc_id"] == "123"
        assert "img1.png" in vector_record["attributes"]["images"]

    def test_pdf_parser_with_images(self, sample_pdf, tmp_path):
        parser = PDFParser()
        chunks = parser.parse(sample_pdf, str(tmp_path / "images"))
        
        assert len(chunks) == 1
        assert "Test PDF Content" in chunks[0].content
        assert (tmp_path / "images").exists()

    def test_html_parser_cleanup(self, sample_html):
        parser = HTMLParser()
        chunks = parser.parse(sample_html)
        
        assert "console.log('test')" not in chunks[0].content
        assert "Test HTML" in chunks[0].content
        assert chunks[0].image_paths == ["test.png"]

    def test_fixed_size_chunking(self):
        strategy = FixedSizeChunkStrategy(chunk_size=10, overlap=2)
        test_content = "This is a sample text for testing chunking functionality."
        chunks = strategy.chunk(test_content, {"doc_id": "123"})
        
        assert len(chunks) == 6
        assert chunks[0].content == "This is a"
        assert chunks[1].content == "a sample"
        assert chunks[0].metadata["chunk_index"] == 0

    def test_whole_document_strategy(self):
        strategy = WholeDocumentStrategy()
        test_content = "Full document content"
        chunks = strategy.chunk(test_content, {"doc_id": "456"})
        
        assert len(chunks) == 1
        assert chunks[0].content == test_content