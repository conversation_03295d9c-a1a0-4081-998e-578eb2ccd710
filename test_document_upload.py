import asyncio
import os
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB
from app.core.embedding import OpenAIEmbedding

# Load environment variables
load_dotenv()

# Get Milvus connection parameters from environment
uri = os.getenv("VECTOR_DB_URI", "")
token = os.getenv("VECTOR_DB_TOKEN", "")

# Get OpenAI API key
openai_api_key = os.getenv("OPENAI_API_KEY", "")

async def test_document_upload():
    print("Testing document upload process...")
    
    # Create embedding model
    model = OpenAIEmbedding(api_key=openai_api_key)
    
    # Create MilvusVectorDB instance
    db = MilvusVectorDB(uri=uri, token=token)
    
    # Connect to Milvus
    await db.connect()
    print("Successfully connected to Milvus")
    
    # Create a test collection
    collection_name = "test_document_upload"
    
    # Sample document chunks
    chunks = [
        "This is the first chunk of the test document.",
        "This is the second chunk of the test document.",
        "This is the third chunk of the test document."
    ]
    
    # Create collection with the model's dimension
    dim = model.get_dimension()
    await db.create_collection(collection_name, dim)
    print(f"Created collection '{collection_name}' with dimension {dim}")
    
    # Process chunks
    print("Processing document chunks...")
    vectors = []
    
    for i, chunk in enumerate(chunks):
        # Generate vector
        vec = await model.agenerate(chunk)
        vectors.append(vec)
        print(f"Generated vector for chunk {i+1}/{len(chunks)}")
    
    # Insert vectors
    try:
        records = [
            {"content": c, "vector": v, "metadata": {"doc_id": "test_doc", "chunk_index": i}}
            for i, (c, v) in enumerate(zip(chunks, vectors))
        ]
        
        result = await db.insert_vectors(collection_name, records)
        print(f"Successfully inserted {result} vectors")
    except Exception as e:
        print(f"Error inserting vectors: {e}")
    
    # Search vectors
    try:
        query_text = "test document"
        query_vector = await model.agenerate(query_text)
        
        results = await db.search_vectors(collection_name, query_vector, 3)
        print(f"Search results for '{query_text}':")
        for i, r in enumerate(results):
            print(f"  {i+1}. Content: {r.get('content', '')[:50]}...")
            print(f"     Distance: {r.get('distance', 0)}")
    except Exception as e:
        print(f"Error searching vectors: {e}")
    
    print("Test completed")

if __name__ == "__main__":
    asyncio.run(test_document_upload())
