#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Milvus向量数据库迁移工具
专门解决16384限制问题的简单有效方案
"""

import os
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
from app.core.vectordb import <PERSON>l<PERSON>sVectorDB
from migration_config import SOURCE_CONFIG, TARGET_CONFIG, MIGRATION_CONFIG, LOGGING_CONFIG

# 加载环境变量
load_dotenv()

class SimpleMilvusMigrator:
    def __init__(self):
        # 源数据库配置
        self.source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
        self.source_token = SOURCE_CONFIG.get("token") or os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
        
        # 目标数据库配置
        self.target_uri = TARGET_CONFIG.get("uri", "")
        self.target_token = TARGET_CONFIG.get("token", "")
        self.target_database = TARGET_CONFIG.get("database", "")
        
        # 迁移配置 - 使用更安全的批次大小
        self.batch_size = min(MIGRATION_CONFIG.get("batch_size", 1000), 500)  # 限制最大批次大小
        self.verify_after_migration = MIGRATION_CONFIG.get("verify_after_migration", True)
        
        # 数据库连接
        self.source_db = None
        self.target_db = None
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        log_level = getattr(logging, LOGGING_CONFIG.get("log_level", "INFO"))
        log_file = "simple_migration.log"
        
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        self.logger = logging.getLogger('SimpleMilvusMigrator')
        self.logger.setLevel(log_level)
        
        # 清除现有处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    async def connect_databases(self) -> bool:
        """连接源数据库和目标数据库"""
        self.logger.info("开始连接数据库...")
        
        try:
            # 连接源数据库
            self.logger.info(f"连接源数据库: {self.source_uri}")
            self.source_db = MilvusVectorDB(
                uri=self.source_uri, 
                token=self.source_token, 
                database=self.source_database
            )
            await self.source_db.connect()
            self.logger.info("源数据库连接成功")
            
            # 连接目标数据库
            self.logger.info(f"连接目标数据库: {self.target_uri}")
            self.target_db = MilvusVectorDB(
                uri=self.target_uri, 
                token=self.target_token, 
                database=self.target_database
            )
            await self.target_db.connect()
            self.logger.info("目标数据库连接成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    async def get_collections_to_migrate(self) -> List[str]:
        """获取需要迁移的集合列表"""
        await self.source_db._refresh_collections()
        all_collections = list(self.source_db.collections_cache)
        return all_collections
    
    def get_vector_dimension_from_user(self, collection_name: str) -> Optional[int]:
        """让用户手动输入向量维度"""
        print(f"\n⚠️ 需要手动输入集合 '{collection_name}' 的向量维度")
        print("常见向量维度:")
        print("  - 384  (sentence-transformers/all-MiniLM-L6-v2)")
        print("  - 768  (BERT base)")
        print("  - 1536 (OpenAI text-embedding-ada-002)")
        print("  - 3072 (OpenAI text-embedding-3-large)")
        
        while True:
            try:
                dim_input = input(f"请输入 '{collection_name}' 的向量维度: ").strip()
                if dim_input:
                    dim = int(dim_input)
                    if dim > 0:
                        self.logger.info(f"用户输入向量维度: {dim}")
                        return dim
                    else:
                        print("❌ 向量维度必须是正整数")
                else:
                    print("❌ 请输入向量维度")
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                self.logger.error("用户取消输入")
                return None
    
    async def get_vector_dimension(self, collection_name: str) -> Optional[int]:
        """获取集合的向量维度"""
        try:
            # 通过查询少量数据获取向量维度
            self.logger.info(f"尝试通过查询数据获取 {collection_name} 的向量维度")
            query_results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["vector"],
                limit=1
            )
            
            if query_results and len(query_results) > 0:
                vector = query_results[0].get('vector')
                if vector and isinstance(vector, list):
                    dim = len(vector)
                    self.logger.info(f"通过查询数据获取到向量维度: {dim}")
                    return dim
        except Exception as e:
            self.logger.warning(f"通过查询数据获取向量维度失败: {e}")
        
        # 让用户手动输入
        return self.get_vector_dimension_from_user(collection_name)
    
    async def get_safe_batch_data(self, collection_name: str, offset: int, limit: int) -> List[Dict[str, Any]]:
        """安全获取批量数据，避免16384限制"""
        # 检查是否会超过限制
        if offset + limit > 16000:  # 使用16000作为安全限制，留一些余量
            self.logger.warning(f"offset({offset}) + limit({limit}) 接近16384限制，调整批次大小")
            limit = max(1, 16000 - offset)
            if limit <= 0:
                self.logger.warning("已达到查询限制，无法获取更多数据")
                return []
        
        try:
            self.logger.debug(f"查询数据: offset={offset}, limit={limit}")
            results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["*"],
                offset=offset,
                limit=limit
            )
            return results if results else []
            
        except Exception as e:
            self.logger.error(f"获取批量数据失败: {e}")
            return []
    
    async def insert_batch_data(self, collection_name: str, data: List[Dict[str, Any]]) -> bool:
        """插入批量数据"""
        try:
            # 数据格式转换
            records = []
            for item in data:
                record = {}
                for key, value in item.items():
                    if key == 'id':
                        continue  # 跳过自动生成的id字段
                    elif key == 'metadata':
                        # 确保metadata是字典格式
                        if isinstance(value, str):
                            try:
                                record['metadata'] = json.loads(value)
                            except:
                                record['metadata'] = {}
                        else:
                            record['metadata'] = value if isinstance(value, dict) else {}
                    else:
                        record[key] = value
                records.append(record)
            
            await self.target_db.insert_vectors(collection_name, records)
            return True
            
        except Exception as e:
            self.logger.error(f"插入批量数据失败: {e}")
            return False
    
    async def migrate_collection_safe(self, collection_name: str) -> bool:
        """安全迁移单个集合，避免16384限制"""
        self.logger.info(f"开始安全迁移集合: {collection_name}")
        
        try:
            # 1. 获取向量维度
            vector_dim = await self.get_vector_dimension(collection_name)
            if vector_dim is None:
                self.logger.error(f"无法获取集合 {collection_name} 的向量维度")
                return False
            
            # 2. 创建目标集合
            self.logger.info(f"创建目标集合 {collection_name}，维度: {vector_dim}")
            await self.target_db.create_collection(collection_name, vector_dim)
            
            # 3. 安全分批迁移数据
            migrated_count = 0
            offset = 0
            max_safe_offset = 15000  # 安全偏移量限制
            
            while offset < max_safe_offset:
                # 计算这批的大小
                remaining_safe = max_safe_offset - offset
                actual_batch_size = min(self.batch_size, remaining_safe)
                
                if actual_batch_size <= 0:
                    break
                
                self.logger.info(f"处理批次: offset={offset}, limit={actual_batch_size}")
                
                # 获取批量数据
                batch_data = await self.get_safe_batch_data(collection_name, offset, actual_batch_size)
                
                if not batch_data:
                    self.logger.info(f"偏移量 {offset} 处无更多数据，迁移完成")
                    break
                
                # 插入数据
                success = await self.insert_batch_data(collection_name, batch_data)
                
                if success:
                    migrated_count += len(batch_data)
                    self.logger.info(f"已迁移: {migrated_count} 条记录")
                else:
                    self.logger.error(f"批次迁移失败，偏移量: {offset}")
                    return False
                
                offset += len(batch_data)
                
                # 如果这批数据少于请求的数量，说明已经到末尾了
                if len(batch_data) < actual_batch_size:
                    self.logger.info("已到达数据末尾，迁移完成")
                    break
            
            if offset >= max_safe_offset:
                self.logger.warning(f"达到安全偏移量限制 {max_safe_offset}，可能还有数据未迁移")
                self.logger.info("建议：使用更新版本的Milvus或分多次运行迁移")
            
            self.logger.info(f"集合 {collection_name} 迁移完成，总计: {migrated_count} 条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"迁移集合 {collection_name} 失败: {e}")
            return False
    
    async def run_migration(self, selected_collections: List[str] = None):
        """运行迁移任务"""
        self.logger.info("开始简化版迁移任务")
        
        # 连接数据库
        if not await self.connect_databases():
            self.logger.error("数据库连接失败，退出迁移")
            return
        
        # 获取要迁移的集合
        if selected_collections:
            collections = selected_collections
        else:
            collections = await self.get_collections_to_migrate()
        
        if not collections:
            self.logger.info("没有需要迁移的集合")
            return
        
        self.logger.info(f"将迁移 {len(collections)} 个集合: {collections}")
        
        # 开始迁移
        success_count = 0
        failed_collections = []
        
        start_time = time.time()
        
        for i, collection in enumerate(collections, 1):
            self.logger.info(f"[{i}/{len(collections)}] 处理集合: {collection}")
            
            success = await self.migrate_collection_safe(collection)
            
            if success:
                success_count += 1
                self.logger.info(f"✅ 集合 {collection} 迁移成功")
            else:
                failed_collections.append(collection)
                self.logger.error(f"❌ 集合 {collection} 迁移失败")
        
        # 迁移总结
        end_time = time.time()
        duration = end_time - start_time
        
        self.logger.info("=" * 60)
        self.logger.info("迁移任务完成")
        self.logger.info(f"总耗时: {duration:.2f} 秒")
        self.logger.info(f"成功迁移: {success_count} 个集合")
        self.logger.info(f"失败集合: {len(failed_collections)} 个")
        
        if failed_collections:
            self.logger.error(f"失败的集合: {failed_collections}")
        else:
            self.logger.info("🎉 所有集合迁移成功！")

async def main():
    """主函数"""
    print("🔧 简化版Milvus向量数据库迁移工具")
    print("专门解决16384查询限制问题")
    print("=" * 60)
    
    migrator = SimpleMilvusMigrator()
    
    print("\n选择操作:")
    print("1. 迁移所有集合")
    print("2. 迁移指定集合")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        await migrator.run_migration()
    elif choice == "2":
        # 获取集合列表供选择
        if await migrator.connect_databases():
            await migrator.source_db._refresh_collections()
            collections = list(migrator.source_db.collections_cache)
            
            print("\n可用集合:")
            for i, col in enumerate(collections, 1):
                print(f"  {i}. {col}")
            
            selection = input("请输入集合序号 (用逗号分隔): ").strip()
            try:
                indices = [int(x.strip()) - 1 for x in selection.split(',')]
                selected = [collections[i] for i in indices if 0 <= i < len(collections)]
                await migrator.run_migration(selected)
            except:
                print("输入格式错误")
    else:
        print("无效选择")

if __name__ == "__main__":
    asyncio.run(main())
