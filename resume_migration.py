#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恢复迁移脚本 - 专门处理查询结果大小限制问题
支持从中断点继续迁移
"""

import os
import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB
from migration_config import SOURCE_CONFIG, TARGET_CONFIG

# 加载环境变量
load_dotenv()

class ResumeMigrator:
    def __init__(self):
        # 数据库配置
        self.source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
        self.source_token = SOURCE_CONFIG.get("token") or os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
        
        self.target_uri = TARGET_CONFIG.get("uri", "")
        self.target_token = TARGET_CONFIG.get("token", "")
        self.target_database = TARGET_CONFIG.get("database", "")
        
        # 数据库连接
        self.source_db = None
        self.target_db = None
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('resume_migration.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('ResumeMigrator')
    
    async def connect_databases(self) -> bool:
        """连接数据库"""
        try:
            self.logger.info(f"连接源数据库: {self.source_uri}")
            self.source_db = MilvusVectorDB(
                uri=self.source_uri, 
                token=self.source_token, 
                database=self.source_database
            )
            await self.source_db.connect()
            
            self.logger.info(f"连接目标数据库: {self.target_uri}")
            self.target_db = MilvusVectorDB(
                uri=self.target_uri, 
                token=self.target_token, 
                database=self.target_database
            )
            await self.target_db.connect()
            
            self.logger.info("数据库连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def get_current_progress(self, collection_name: str) -> int:
        """获取当前迁移进度"""
        try:
            target_stats = self.target_db.client.get_collection_stats(collection_name)
            current_count = target_stats.get('row_count', 0)
            self.logger.info(f"目标集合 {collection_name} 当前记录数: {current_count}")
            return current_count
        except Exception as e:
            self.logger.warning(f"获取目标集合统计失败: {e}")
            return 0
    
    async def get_small_batch_data(self, collection_name: str, offset: int, limit: int = 50):
        """获取小批量数据，避免大小限制"""
        try:
            self.logger.debug(f"获取数据: offset={offset}, limit={limit}")
            results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["*"],
                offset=offset,
                limit=limit
            )
            return results
        except Exception as e:
            if "exceed the limit size" in str(e).lower():
                # 如果还是太大，进一步减少
                if limit > 10:
                    self.logger.warning(f"批次大小 {limit} 仍然太大，减少到 {limit//2}")
                    return await self.get_small_batch_data(collection_name, offset, limit//2)
                else:
                    self.logger.error(f"即使批次大小为 {limit} 也无法获取数据: {e}")
                    return []
            else:
                self.logger.error(f"获取数据失败: {e}")
                return []
    
    async def resume_migration(self, collection_name: str):
        """恢复迁移指定集合"""
        self.logger.info(f"开始恢复迁移集合: {collection_name}")
        
        try:
            # 1. 获取源集合总数
            source_stats = self.source_db.client.get_collection_stats(collection_name)
            total_count = source_stats.get('row_count', 0)
            self.logger.info(f"源集合总记录数: {total_count}")
            
            # 2. 获取目标集合当前进度
            current_count = self.get_current_progress(collection_name)
            
            if current_count >= total_count:
                self.logger.info(f"集合 {collection_name} 已完成迁移")
                return True
            
            # 3. 从当前进度继续迁移
            remaining = total_count - current_count
            self.logger.info(f"需要继续迁移 {remaining} 条记录 (从第 {current_count + 1} 条开始)")
            
            # 4. 使用小批次迁移剩余数据
            offset = current_count
            batch_size = 50  # 使用较小的批次大小
            migrated_count = current_count
            
            while offset < total_count:
                actual_batch_size = min(batch_size, total_count - offset)
                self.logger.info(f"迁移批次: {offset + 1} - {offset + actual_batch_size}")
                
                # 获取数据
                batch_data = await self.get_small_batch_data(collection_name, offset, actual_batch_size)
                
                if not batch_data:
                    self.logger.warning(f"偏移量 {offset} 处无数据，跳过")
                    offset += actual_batch_size
                    continue
                
                # 处理数据
                records = []
                for item in batch_data:
                    record = {}
                    for key, value in item.items():
                        if key == 'id':
                            continue
                        elif key == 'metadata':
                            if isinstance(value, str):
                                try:
                                    record['metadata'] = json.loads(value)
                                except:
                                    record['metadata'] = {}
                            else:
                                record['metadata'] = value if isinstance(value, dict) else {}
                        else:
                            record[key] = value
                    records.append(record)
                
                # 插入数据
                try:
                    await self.target_db.insert_vectors(collection_name, records)
                    migrated_count += len(batch_data)
                    progress_percent = (migrated_count / total_count) * 100
                    self.logger.info(f"已迁移: {migrated_count}/{total_count} ({progress_percent:.1f}%)")
                except Exception as e:
                    self.logger.error(f"插入数据失败: {e}")
                    return False
                
                offset += len(batch_data)
                
                # 每迁移一定数量后暂停一下
                if migrated_count % 500 == 0:
                    await asyncio.sleep(1)
            
            self.logger.info(f"集合 {collection_name} 迁移完成！总计: {migrated_count} 条记录")
            
            # 5. 验证结果
            await asyncio.sleep(3)  # 等待统计更新
            final_count = self.get_current_progress(collection_name)
            
            if final_count >= total_count:
                self.logger.info(f"✅ 验证成功: {collection_name} ({final_count} 条记录)")
                return True
            else:
                self.logger.warning(f"⚠️ 验证结果: 源:{total_count} 目标:{final_count}")
                return True  # 仍然认为成功，可能是统计延迟
            
        except Exception as e:
            self.logger.error(f"恢复迁移失败: {e}")
            return False

async def main():
    """主函数"""
    print("🔧 恢复迁移工具 - 处理查询大小限制问题")
    print("=" * 60)
    
    migrator = ResumeMigrator()
    
    if not await migrator.connect_databases():
        return
    
    # 获取集合列表
    await migrator.source_db._refresh_collections()
    collections = list(migrator.source_db.collections_cache)
    
    print(f"\n发现 {len(collections)} 个集合:")
    for i, col in enumerate(collections, 1):
        print(f"  {i}. {col}")
    
    # 选择要恢复的集合
    while True:
        try:
            choice = input("\n请输入要恢复迁移的集合序号: ").strip()
            if choice:
                index = int(choice) - 1
                if 0 <= index < len(collections):
                    selected_collection = collections[index]
                    break
                else:
                    print("❌ 序号超出范围")
            else:
                print("❌ 请输入序号")
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            return
    
    print(f"\n开始恢复迁移集合: {selected_collection}")
    
    success = await migrator.resume_migration(selected_collection)
    
    if success:
        print(f"✅ 集合 {selected_collection} 恢复迁移成功！")
    else:
        print(f"❌ 集合 {selected_collection} 恢复迁移失败")

if __name__ == "__main__":
    asyncio.run(main())
