#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整数据迁移工具 - 保证源collection完全写入到目标collection
确保数据完全一致，不跳过任何记录
"""

import os
import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from app.core.vectordb import MilvusVectorDB
from migration_config import SOURCE_CONFIG, TARGET_CONFIG

# 加载环境变量
load_dotenv()

class CompleteMigrator:
    def __init__(self):
        # 数据库配置
        self.source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
        self.source_token = SOURCE_CONFIG.get("token") or os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
        
        self.target_uri = TARGET_CONFIG.get("uri", "")
        self.target_token = TARGET_CONFIG.get("token", "")
        self.target_database = TARGET_CONFIG.get("database", "")
        
        # 数据库连接
        self.source_db = None
        self.target_db = None
        
        # 迁移统计
        self.total_records = 0
        self.migrated_records = 0
        self.failed_records = []
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('complete_migration.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('CompleteMigrator')
    
    async def connect_databases(self) -> bool:
        """连接数据库"""
        try:
            self.logger.info(f"连接源数据库: {self.source_uri}")
            self.source_db = MilvusVectorDB(
                uri=self.source_uri, 
                token=self.source_token, 
                database=self.source_database
            )
            await self.source_db.connect()
            
            self.logger.info(f"连接目标数据库: {self.target_uri}")
            self.target_db = MilvusVectorDB(
                uri=self.target_uri, 
                token=self.target_token, 
                database=self.target_database
            )
            await self.target_db.connect()
            
            self.logger.info("数据库连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def get_vector_dimension_from_user(self, collection_name: str) -> Optional[int]:
        """让用户输入向量维度"""
        print(f"\n⚠️ 请输入集合 '{collection_name}' 的向量维度")
        print("常见维度: 384, 768, 1536, 3072, 12288")
        
        while True:
            try:
                dim_input = input(f"向量维度: ").strip()
                if dim_input:
                    dim = int(dim_input)
                    if dim > 0:
                        return dim
                    else:
                        print("❌ 向量维度必须是正整数")
                else:
                    print("❌ 请输入向量维度")
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                return None
    
    async def get_record_ids_batch(self, collection_name: str, offset: int, limit: int) -> List[int]:
        """获取记录ID批次"""
        try:
            results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["id"],
                offset=offset,
                limit=limit
            )
            return [r['id'] for r in results if 'id' in r]
        except Exception as e:
            self.logger.error(f"获取ID批次失败 (offset: {offset}): {e}")
            return []
    
    async def get_single_record_complete(self, collection_name: str, record_id: int) -> Optional[Dict[str, Any]]:
        """获取单条完整记录，确保所有字段都被获取"""
        record = {}
        
        # 定义字段获取策略
        field_strategies = [
            # 策略1: 尝试获取所有字段
            {"fields": ["*"], "name": "完整查询"},
            # 策略2: 分组获取字段
            {"fields": ["content", "collection_name"], "name": "基本字段"},
            {"fields": ["metadata"], "name": "元数据字段"},
            {"fields": ["vector"], "name": "向量字段"},
        ]
        
        # 首先尝试完整查询
        try:
            result = self.source_db.client.query(
                collection_name=collection_name,
                filter=f"id == {record_id}",
                output_fields=["*"],
                limit=1
            )
            
            if result and len(result) > 0:
                # 移除id字段
                complete_record = {k: v for k, v in result[0].items() if k != 'id'}
                self.logger.debug(f"记录 {record_id} 完整查询成功")
                return complete_record
                
        except Exception as e:
            if "exceed the limit size" in str(e).lower():
                self.logger.warning(f"记录 {record_id} 太大，使用分字段策略")
            else:
                self.logger.error(f"记录 {record_id} 完整查询失败: {e}")
        
        # 分字段获取策略
        all_fields = set()
        
        for strategy in field_strategies[1:]:  # 跳过完整查询策略
            try:
                result = self.source_db.client.query(
                    collection_name=collection_name,
                    filter=f"id == {record_id}",
                    output_fields=strategy["fields"],
                    limit=1
                )
                
                if result and len(result) > 0:
                    for field, value in result[0].items():
                        if field != 'id':
                            record[field] = value
                            all_fields.add(field)
                    self.logger.debug(f"记录 {record_id} {strategy['name']}获取成功")
                else:
                    self.logger.warning(f"记录 {record_id} {strategy['name']}返回空")
                    
            except Exception as e:
                self.logger.error(f"记录 {record_id} {strategy['name']}失败: {e}")
                # 为失败的字段设置默认值
                for field in strategy["fields"]:
                    if field not in record and field != "*":
                        if field == 'vector':
                            record[field] = []
                        elif field == 'metadata':
                            record[field] = {}
                        else:
                            record[field] = ""
        
        # 检查必需字段
        required_fields = ['content', 'collection_name', 'metadata', 'vector']
        missing_fields = []
        
        for field in required_fields:
            if field not in record:
                missing_fields.append(field)
                # 设置默认值
                if field == 'vector':
                    record[field] = []
                elif field == 'metadata':
                    record[field] = {}
                else:
                    record[field] = ""
        
        if missing_fields:
            self.logger.error(f"记录 {record_id} 缺少字段: {missing_fields}，已设置默认值")
        
        # 尝试获取动态字段
        try:
            # 获取集合schema以了解所有可能的字段
            collection_info = self.source_db.client.describe_collection(collection_name)
            if 'fields' in collection_info:
                for field_info in collection_info['fields']:
                    field_name = field_info.get('name', '')
                    if field_name and field_name not in record and field_name != 'id':
                        try:
                            field_result = self.source_db.client.query(
                                collection_name=collection_name,
                                filter=f"id == {record_id}",
                                output_fields=[field_name],
                                limit=1
                            )
                            if field_result and len(field_result) > 0:
                                record[field_name] = field_result[0].get(field_name)
                        except:
                            pass  # 忽略动态字段获取失败
        except:
            pass  # 忽略schema获取失败
        
        if record:
            self.logger.info(f"记录 {record_id} 分字段获取完成，字段数: {len(record)}")
            return record
        else:
            self.logger.error(f"❌ 记录 {record_id} 完全获取失败")
            return None
    
    async def migrate_collection_complete(self, collection_name: str, start_offset: int = 0):
        """完整迁移集合，确保数据完全一致"""
        self.logger.info(f"开始完整迁移集合: {collection_name}")
        
        try:
            # 1. 获取源集合信息
            source_stats = self.source_db.client.get_collection_stats(collection_name)
            self.total_records = source_stats.get('row_count', 0)
            self.logger.info(f"源集合总记录数: {self.total_records}")
            
            if self.total_records == 0:
                self.logger.warning(f"集合 {collection_name} 为空")
                return True
            
            # 2. 获取向量维度
            vector_dim = self.get_vector_dimension_from_user(collection_name)
            if vector_dim is None:
                self.logger.error("无法获取向量维度")
                return False
            
            # 3. 创建目标集合
            self.logger.info(f"创建目标集合，维度: {vector_dim}")
            await self.target_db.create_collection(collection_name, vector_dim)
            
            # 4. 获取当前进度
            try:
                target_stats = self.target_db.client.get_collection_stats(collection_name)
                current_count = target_stats.get('row_count', 0)
            except:
                current_count = 0
            
            start_offset = max(start_offset, current_count)
            self.migrated_records = current_count
            
            self.logger.info(f"从偏移量 {start_offset} 开始迁移")
            
            # 5. 逐批次迁移
            batch_size = 100  # ID查询批次大小
            offset = start_offset
            
            while offset < self.total_records:
                actual_batch_size = min(batch_size, self.total_records - offset)
                self.logger.info(f"处理批次: {offset + 1} - {offset + actual_batch_size}")
                
                # 获取ID列表
                record_ids = await self.get_record_ids_batch(collection_name, offset, actual_batch_size)
                
                if not record_ids:
                    self.logger.warning(f"偏移量 {offset} 处无数据，跳过")
                    offset += actual_batch_size
                    continue
                
                # 逐个处理记录
                batch_records = []
                for record_id in record_ids:
                    record = await self.get_single_record_complete(collection_name, record_id)
                    
                    if record:
                        # 处理数据格式
                        processed_record = {}
                        for key, value in record.items():
                            if key == 'metadata':
                                if isinstance(value, str):
                                    try:
                                        processed_record['metadata'] = json.loads(value)
                                    except:
                                        processed_record['metadata'] = {}
                                else:
                                    processed_record['metadata'] = value if isinstance(value, dict) else {}
                            else:
                                processed_record[key] = value
                        
                        batch_records.append(processed_record)
                    else:
                        self.failed_records.append(record_id)
                        self.logger.error(f"❌ 记录 {record_id} 获取失败，将影响数据完整性")
                
                # 插入批次数据
                if batch_records:
                    try:
                        await self.target_db.insert_vectors(collection_name, batch_records)
                        self.migrated_records += len(batch_records)
                        progress_percent = (self.migrated_records / self.total_records) * 100
                        self.logger.info(f"已迁移: {self.migrated_records}/{self.total_records} ({progress_percent:.1f}%)")
                    except Exception as e:
                        self.logger.error(f"插入批次数据失败: {e}")
                        return False
                
                offset += len(record_ids)
                
                # 定期暂停
                if self.migrated_records % 1000 == 0:
                    await asyncio.sleep(1)
            
            # 6. 最终验证
            self.logger.info("开始最终验证...")
            await asyncio.sleep(5)  # 等待统计更新
            
            try:
                final_target_stats = self.target_db.client.get_collection_stats(collection_name)
                final_count = final_target_stats.get('row_count', 0)
            except:
                final_count = 0
            
            # 输出迁移报告
            self.logger.info("=" * 60)
            self.logger.info("迁移完成报告:")
            self.logger.info(f"源集合记录数: {self.total_records}")
            self.logger.info(f"目标集合记录数: {final_count}")
            self.logger.info(f"成功迁移记录数: {self.migrated_records}")
            self.logger.info(f"失败记录数: {len(self.failed_records)}")
            
            if self.failed_records:
                self.logger.error(f"失败的记录ID: {self.failed_records}")
                self.logger.error("❌ 数据迁移不完整！")
                return False
            
            if final_count >= self.total_records:
                self.logger.info("✅ 数据迁移完整成功！")
                return True
            else:
                diff = self.total_records - final_count
                self.logger.warning(f"⚠️ 目标记录数少于源记录数，差异: {diff}")
                return False
            
        except Exception as e:
            self.logger.error(f"迁移过程失败: {e}")
            return False

async def main():
    """主函数"""
    print("🔧 完整数据迁移工具 - 保证数据完全一致")
    print("=" * 60)
    
    migrator = CompleteMigrator()
    
    if not await migrator.connect_databases():
        return
    
    # 获取集合列表
    await migrator.source_db._refresh_collections()
    collections = list(migrator.source_db.collections_cache)
    
    print(f"\n发现 {len(collections)} 个集合:")
    for i, col in enumerate(collections, 1):
        print(f"  {i}. {col}")
    
    # 选择集合
    while True:
        try:
            choice = input("\n请输入要迁移的集合序号: ").strip()
            if choice:
                index = int(choice) - 1
                if 0 <= index < len(collections):
                    selected_collection = collections[index]
                    break
                else:
                    print("❌ 序号超出范围")
            else:
                print("❌ 请输入序号")
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            return
    
    # 询问起始偏移量
    start_offset = 0
    try:
        offset_input = input(f"\n请输入起始偏移量 (默认0，继续迁移请输入如13000): ").strip()
        if offset_input:
            start_offset = int(offset_input)
    except ValueError:
        print("使用默认偏移量0")
    
    print(f"\n开始完整迁移集合: {selected_collection}")
    print("⚠️ 此工具确保数据完全一致，不会跳过任何记录")
    
    success = await migrator.migrate_collection_complete(selected_collection, start_offset)
    
    if success:
        print(f"✅ 集合 {selected_collection} 完整迁移成功！")
    else:
        print(f"❌ 集合 {selected_collection} 迁移失败或不完整")
        if migrator.failed_records:
            print(f"失败的记录ID: {migrator.failed_records}")

if __name__ == "__main__":
    asyncio.run(main())
