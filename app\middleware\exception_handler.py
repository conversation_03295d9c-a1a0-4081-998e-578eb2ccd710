"""
异常处理中间件
处理应用程序的全局异常和请求验证错误
"""
import json
import logging
import traceback
from typing import Any, Dict

from fastapi import Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

# 使用标准logging
logger = logging.getLogger(__name__)


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    处理请求验证错误
    """
    logger.error(f"请求体解析错误: {exc}")
    logger.error(f"请求URL: {request.url}")
    logger.error(f"请求方法: {request.method}")
    logger.error(f"Content-Type: {request.headers.get('content-type')}")
    
    # 尝试获取原始请求体
    body_str = ""
    try:
        body = await request.body()
        body_str = body.decode('utf-8', errors='replace')
        logger.error(f"原始请求体: {body_str}")
        logger.error(f"请求体长度: {len(body)} 字节")
        
        # 尝试解析JSON
        if request.headers.get('content-type') == 'application/json':
            try:
                json_data = json.loads(body_str)
                logger.error(f"JSON解析结果: {json_data}")
            except json.JSONDecodeError as json_err:
                logger.error(f"JSON解析失败: {json_err}")
                logger.error(f"JSON错误位置: 第 {json_err.lineno} 行, 第 {json_err.colno} 列")
    except Exception as e:
        logger.error(f"无法读取请求体: {e}")
    
    # 记录验证错误详情
    for error in exc.errors():
        logger.error(f"验证错误: {error}")
    
    return JSONResponse(
        status_code=422,
        content={
            "detail": "请求体解析错误，请检查请求格式",
            "errors": exc.errors(),
            "body": body_str if body_str else None,
            "content_type": request.headers.get('content-type')
        },
    )


async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    处理全局异常
    """
    logger.error(f"全局异常: {exc}")
    logger.error(f"异常类型: {type(exc).__name__}")
    logger.error(f"请求URL: {request.url}")
    logger.error(f"请求方法: {request.method}")
    logger.error(f"Content-Type: {request.headers.get('content-type')}")
    
    # 尝试获取原始请求体
    body_str = ""
    try:
        body = await request.body()
        body_str = body.decode('utf-8', errors='replace')
        logger.error(f"原始请求体: {body_str}")
        
        # 尝试解析JSON
        if request.headers.get('content-type') == 'application/json':
            try:
                json_data = json.loads(body_str)
                logger.error(f"JSON解析结果: {json_data}")
            except json.JSONDecodeError as json_err:
                logger.error(f"JSON解析失败: {json_err}")
    except Exception as e:
        logger.error(f"无法读取请求体: {e}")
    
    # 记录详细的错误堆栈
    logger.error(f"详细错误信息:\n{traceback.format_exc()}")
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": f"服务器内部错误: {str(exc)}",
            "type": str(type(exc).__name__),
            "traceback": traceback.format_exc().split('\n') if logger.level <= 10 else None,  # 只在DEBUG级别显示堆栈
            "body": body_str if body_str else None,
            "content_type": request.headers.get('content-type')
        },
    )


def setup_exception_handlers(app) -> None:
    """
    为FastAPI应用设置异常处理器
    """
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, global_exception_handler) 