from pydantic_settings import BaseSettings, SettingsConfigDict
import os

class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",  # 忽略额外的环境变量
        case_sensitive=False  # 忽略环境变量名称大小写
    )
    DATABASE_TYPE: str = 'milvus'
    MILVUS_HOST: str = 'localhost'
    MILVUS_PORT: int = 19530
    FAISS_PERSIST_PATH: str = './faiss_index'

class ModelConfig(BaseSettings):
    model_config = SettingsConfigDict(
        protected_namespaces=('settings_',),
        env_prefix="MODEL_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",  # 忽略额外的环境变量
        case_sensitive=False  # 忽略环境变量名称大小写
    )
    model_type: str = 'huggingface'
    model_name: str = 'nlp_gte_sentence-embedding_chinese-small'
    model_path: str = 'D:\\models\\nlp_gte_sentence-embedding_chinese-small'
    device: str = 'cpu'
    cache_dir: str = '~/.cache/huggingface/hub'

class VectorDBConfig(BaseSettings):
    model_config = SettingsConfigDict(
        protected_namespaces=('settings_',),
        env_prefix="VECTOR_DB_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",  # 忽略额外的环境变量
        case_sensitive=False  # 忽略环境变量名称大小写
    )
    db_type: str = 'milvus'
    # milvus_client连接方式参数
    uri: str = ''
    token: str = ''
    database: str = ''  # 默认数据库名称

class EncryptionConfig(BaseSettings):
    model_config = SettingsConfigDict(
        protected_namespaces=('settings_',),
        env_prefix="ENCRYPTION_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",  # 忽略额外的环境变量
        case_sensitive=False  # 忽略环境变量名称大小写
    )
    # AES加密密钥，如果不提供则自动生成
    key: str = os.environ.get('AES_ENCRYPTION_KEY', '')