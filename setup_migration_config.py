#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus迁移配置设置工具
帮助用户快速创建和配置迁移配置文件
"""

import os
import json
from pathlib import Path

def create_config_file():
    """创建配置文件"""
    print("🔧 Milvus迁移配置设置工具")
    print("=" * 50)
    
    # 检查是否已存在配置文件
    config_file = Path("migration_config.py")
    if config_file.exists():
        print("⚠️ 配置文件 migration_config.py 已存在")
        overwrite = input("是否覆盖现有配置? (y/N): ").strip().lower()
        if overwrite != 'y':
            print("❌ 用户取消操作")
            return
    
    print("\n=== 配置源数据库 ===")
    print("源数据库配置（当前使用的数据库）")
    print("如果留空，将从环境变量读取")
    
    source_uri = input("源数据库URI (留空从环境变量读取): ").strip()
    source_token = input("源数据库Token (留空从环境变量读取): ").strip()
    source_database = input("源数据库名称 (留空从环境变量读取): ").strip()
    
    print("\n=== 配置目标数据库 ===")
    print("目标数据库配置（数据迁移的目标位置）")
    print("⚠️ 必须填写目标数据库URI")
    
    # 显示常用配置模板
    print("\n常用配置模板:")
    print("1. 本地Milvus: http://localhost:19530")
    print("2. 云端Milvus: https://your-cloud-milvus.com:19530")
    print("3. Zilliz Cloud: https://your-cluster.zillizcloud.com:19530")
    print("4. 自定义配置")
    
    template_choice = input("选择模板 (1-4): ").strip()
    
    if template_choice == "1":
        target_uri = "http://localhost:19530"
        target_token = ""
        target_database = "default"
        print(f"✅ 使用本地Milvus模板: {target_uri}")
    elif template_choice == "2":
        target_uri = input("请输入云端Milvus URI: ").strip()
        target_token = input("请输入云端Milvus Token: ").strip()
        target_database = input("请输入数据库名称 (可选): ").strip()
    elif template_choice == "3":
        target_uri = input("请输入Zilliz Cloud URI: ").strip()
        target_token = input("请输入Zilliz Cloud Token: ").strip()
        target_database = input("请输入数据库名称 (默认default): ").strip() or "default"
    else:
        target_uri = input("目标数据库URI: ").strip()
        target_token = input("目标数据库Token (可选): ").strip()
        target_database = input("目标数据库名称 (可选): ").strip()
    
    if not target_uri:
        print("❌ 目标数据库URI不能为空")
        return
    
    print("\n=== 配置迁移参数 ===")
    
    # 批次大小
    batch_size_input = input("批次大小 (默认1000): ").strip()
    try:
        batch_size = int(batch_size_input) if batch_size_input else 1000
    except ValueError:
        batch_size = 1000
        print("⚠️ 批次大小格式错误，使用默认值1000")
    
    # 验证迁移
    verify_input = input("迁移后验证数据 (Y/n): ").strip().lower()
    verify_migration = verify_input != 'n'
    
    # 跳过已存在的集合
    skip_input = input("跳过已存在的集合 (Y/n): ").strip().lower()
    skip_existing = skip_input != 'n'
    
    # 生成配置文件内容
    config_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus数据迁移配置文件
由 setup_migration_config.py 自动生成
"""

# 源数据库配置（当前使用的数据库）
# 如果留空，将从环境变量 VECTOR_DB_URI, VECTOR_DB_TOKEN, VECTOR_DB_DATABASE 读取
SOURCE_CONFIG = {{
    "uri": "{source_uri}",  # 可选：源Milvus的URI，留空则从环境变量读取
    "token": "{source_token}",  # 可选：源Milvus的Token，留空则从环境变量读取
    "database": "{source_database}",  # 可选：源数据库名称，留空则从环境变量读取
}}

# 目标数据库配置（新的Milvus数据库）
TARGET_CONFIG = {{
    "uri": "{target_uri}",  # 目标Milvus的URI
    "token": "{target_token}",  # 目标Milvus的Token（如果需要认证）
    "database": "{target_database}",  # 目标数据库名称（留空使用默认数据库）
}}

# 迁移配置
MIGRATION_CONFIG = {{
    "batch_size": {batch_size},  # 每批迁移的记录数
    "verify_after_migration": {verify_migration},  # 迁移后是否验证数据
    "skip_existing_collections": {skip_existing},  # 是否跳过已存在的集合
    "preserve_collection_names": True,  # 是否保持原集合名称
}}

# 日志配置
LOGGING_CONFIG = {{
    "log_level": "INFO",
    "log_file": "migration.log",
    "console_output": True,
}}

# 配置验证函数
def validate_config():
    """验证配置是否正确"""
    errors = []
    warnings = []

    # 检查目标配置
    if not TARGET_CONFIG.get("uri"):
        errors.append("TARGET_CONFIG 中的 uri 不能为空")

    # 检查源配置
    import os
    source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
    if not source_uri:
        warnings.append("源数据库URI未配置，请确保环境变量 VECTOR_DB_URI 已设置")

    # 检查批次大小
    batch_size = MIGRATION_CONFIG.get("batch_size", 0)
    if not isinstance(batch_size, int) or batch_size <= 0:
        errors.append("MIGRATION_CONFIG 中的 batch_size 必须是正整数")

    # 输出结果
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {{error}}")

    if warnings:
        print("⚠️ 配置警告:")
        for warning in warnings:
            print(f"  - {{warning}}")

    if not errors and not warnings:
        print("✅ 配置验证通过")

    return len(errors) == 0

def show_current_config():
    """显示当前配置"""
    import os

    print("=== 当前配置 ===")

    # 源数据库配置
    source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
    source_db = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
    print(f"源数据库:")
    print(f"  URI: {{source_uri or '未配置'}}")
    print(f"  Database: {{source_db or '默认'}}")

    # 目标数据库配置
    print(f"目标数据库:")
    print(f"  URI: {{TARGET_CONFIG.get('uri') or '未配置'}}")
    print(f"  Database: {{TARGET_CONFIG.get('database') or '默认'}}")

    # 迁移配置
    print(f"迁移设置:")
    print(f"  批次大小: {{MIGRATION_CONFIG.get('batch_size')}}")
    print(f"  验证迁移: {{MIGRATION_CONFIG.get('verify_after_migration')}}")
    print(f"  跳过已存在: {{MIGRATION_CONFIG.get('skip_existing_collections')}}")

if __name__ == "__main__":
    print("🔧 Milvus迁移配置验证工具")
    print("=" * 50)

    show_current_config()
    print()
    validate_config()
'''
    
    # 写入配置文件
    try:
        with open("migration_config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print("\n✅ 配置文件创建成功！")
        print(f"📁 文件位置: {Path('migration_config.py').absolute()}")
        
        # 验证配置
        print("\n=== 配置验证 ===")
        try:
            # 简单验证配置内容
            print("✅ 配置文件已创建")
            print("💡 运行以下命令来验证配置:")
            print("   python migration_config.py")
        except Exception as e:
            print(f"⚠️ 配置验证时出现问题: {e}")
        
        print("\n=== 下一步操作 ===")
        print("1. 运行迁移脚本:")
        print("   python advanced_migration.py")
        print("2. 或者先验证配置:")
        print("   python migration_config.py")
        
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")

def show_help():
    """显示帮助信息"""
    print("🔧 Milvus迁移配置设置工具")
    print("=" * 50)
    print("此工具帮助您快速创建 migration_config.py 配置文件")
    print()
    print("使用方法:")
    print("  python setup_migration_config.py")
    print()
    print("配置文件说明:")
    print("- SOURCE_CONFIG: 源数据库配置（可留空从环境变量读取）")
    print("- TARGET_CONFIG: 目标数据库配置（必须填写）")
    print("- MIGRATION_CONFIG: 迁移参数配置")
    print("- LOGGING_CONFIG: 日志配置")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        show_help()
    else:
        create_config_file()
