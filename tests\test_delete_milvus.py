import requests

# 按ID删除示例
def delete_by_ids(database: str, collection: str, ids: list, auto_flush: bool = False):
    print("调用按ID删除接口...")
    url = "http://copilot.csvw.com/rag_service/api/v1/delete"
    payload = {
        "database": database,
        "collection": collection,
        "ids": ids,
        "auto_flush": auto_flush
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        print("删除成功:", response.json())
    except requests.exceptions.HTTPError as err:
        print(f"HTTP错误: {err}")
        print("响应内容:", err.response.text)
    except Exception as e:
        print(f"其他错误: {str(e)}")