"""
环境配置管理模块
处理环境变量的加载、验证和调试信息
"""
import os
import logging
from pathlib import Path
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


class EnvironmentManager:
    """环境配置管理器"""
    
    @staticmethod
    def load_environment():
        """加载环境变量配置"""
        logger.info("正在加载.env文件...")
        
        # 显式加载.env文件
        load_dotenv()
        
        # 检查.env文件是否存在
        env_file_path = Path(".env")
        if env_file_path.exists():
            logger.info(f".env文件存在: {env_file_path.absolute()}")
            try:
                env_content = env_file_path.read_text(encoding="utf-8")
                logger.info(f".env文件内容长度: {len(env_content)} 字节")
                EnvironmentManager._log_env_content(env_content)
            except Exception as e:
                logger.error(f"读取.env文件失败: {e}")
        else:
            logger.warning(f".env文件不存在于当前目录: {os.getcwd()}")
        
        # 打印环境变量信息以便调试
        EnvironmentManager._log_environment_variables()
    
    @staticmethod
    def _log_env_content(env_content: str):
        """记录.env文件内容（隐藏敏感信息）"""
        logger.info("环境变量文件内容:")
        for line in env_content.splitlines():
            if line.strip() and not line.strip().startswith("#"):
                if "=" in line:
                    key, value = line.split("=", 1)
                    if any(sensitive in key.upper() for sensitive in ["PASSWORD", "TOKEN", "KEY", "SECRET"]):
                        logger.info(f"  {key}={'*' * len(value)}")
                    else:
                        logger.info(f"  {line}")
    
    @staticmethod
    def _log_environment_variables():
        """记录相关环境变量信息"""
        logger.info("环境变量信息:")
        prefixes = ["MODEL_", "VECTOR_DB_", "AZURE_OPENAI_", "ENCRYPTION_"]
        
        for key, value in os.environ.items():
            if any(key.startswith(prefix) for prefix in prefixes):
                # 如果是密码或API密钥，不显示实际值
                if any(sensitive in key.upper() for sensitive in ["PASSWORD", "API_KEY", "TOKEN", "SECRET", "KEY"]):
                    logger.info(f"  {key} = {'*' * len(value)}")
                else:
                    logger.info(f"  {key} = {value}")
    
    @staticmethod
    def validate_required_env_vars():
        """验证必需的环境变量"""
        required_vars = {
            "MODEL_TYPE": "模型类型",
            "MODEL_NAME": "模型名称",
            "VECTOR_DB_TYPE": "向量数据库类型"
        }
        
        missing_vars = []
        for var, description in required_vars.items():
            if not os.getenv(var):
                missing_vars.append(f"{var} ({description})")
        
        if missing_vars:
            logger.warning(f"缺少以下环境变量: {', '.join(missing_vars)}")
            logger.warning("将使用默认配置")
        else:
            logger.info("所有必需的环境变量都已设置")
    
    @staticmethod
    def get_debug_info():
        """获取调试信息"""
        return {
            "current_dir": os.getcwd(),
            "env_file_exists": Path(".env").exists(),
            "python_path": os.environ.get("PYTHONPATH", "未设置"),
            "path": os.environ.get("PATH", "")[:100] + "...",  # 只显示前100个字符
        }


# 便利函数
def load_and_validate_environment():
    """加载并验证环境配置"""
    env_manager = EnvironmentManager()
    env_manager.load_environment()
    env_manager.validate_required_env_vars()
    return env_manager 