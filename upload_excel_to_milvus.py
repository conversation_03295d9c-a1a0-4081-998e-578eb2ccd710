import time
import asyncio
import requests
import pandas as pd
from pathlib import Path


# 服务地址
API_URL = "http://copilot.csvw.com/rag_service/api/v1/upload_texts"

async def upload_excel_to_milvus():
    print("开始处理Excel文件并上传到Milvus向量库...")

    # 数据库和集合配置
    database = "SVWServiceTest"
    collection = "FaqRewriteQuestionIndexaaaaaaaaaaaaaaa"

    # 查找data目录下的所有Excel文件
    data_dir = Path("app/data")
    if not data_dir.exists():
        print(f"目录 {data_dir} 不存在，正在创建...")
        data_dir.mkdir(exist_ok=True)
        print(f"请将Excel文件放入 {data_dir} 目录后重新运行脚本")
        return

    excel_files = list(data_dir.glob("*.xlsx")) + list(data_dir.glob("*.xls"))

    if not excel_files:
        print(f"在 {data_dir} 目录下未找到Excel文件")
        return

    # 使用找到的第一个Excel文件
    excel_file = excel_files[0]
    print(f"使用Excel文件: {excel_file}")

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"成功读取Excel文件，共 {len(df)} 行数据")

        # 检查是否有问题和答案列
        question_col = None
        answer_col = None

        # 查找问题和答案列
        for col in df.columns:
            col_lower = str(col).lower()
            if "问题-优化" in col_lower or "question" in col_lower:
                question_col = col
            elif "答案" in col_lower or "answer" in col_lower:
                answer_col = col

        # 如果没有找到，尝试使用前两列
        if question_col is None and len(df.columns) >= 1:
            question_col = df.columns[0]
        if answer_col is None and len(df.columns) >= 2:
            answer_col = df.columns[1]

        if question_col is None or answer_col is None:
            print(f"无法识别问题和答案列，请确保Excel文件包含这些列")
            print(f"可用列: {list(df.columns)}")
            return

        print(f"使用列 '{question_col}' 作为问题列")
        print(f"使用列 '{answer_col}' 作为答案列")

        # 处理每一行数据，单条上传
        total_rows = len(df)
        success_count = 0
        error_count = 0

        for index, row in df.iterrows():
            question = str(row[question_col]).strip()
            answer = str(row[answer_col]).strip()

            # 跳过空问题
            if not question or question.lower() == "nan":
                print(f"\n跳过第 {index+1} 行: 问题为空")
                continue

            # 如果答案为空，设置默认值
            if not answer or answer.lower() == "nan":
                answer = "暂无答案"

            print(f"\n处理第 {index+1}/{total_rows} 行数据")
            print(f"问题: {question}")
            print(f"答案: {answer[:50]}{'...' if len(answer) > 50 else ''}")

            # 准备基本元数据
            metadata = {
                "question": question,
                "answer": answer,
                "source": f"Excel导入: {excel_file.name}",
                "row_index": int(index),
                "source_type": "excel_import"
            }

            # 准备请求数据
            request_data = {
                "texts": [question],  # 只使用问题作为向量文本
                "database": database,
                "collection": collection,
                "encrypt": False,  # 不加密内容
                # "embedding_type": "azure-openai",  # 使用Azure OpenAI生成向量
                "metadata": metadata
            }

            # 处理所有其他列作为动态字段
            # 跳过问题和答案列，以及已经处理过的列
            processed_cols = [question_col, answer_col]

            for col in df.columns:
                if col in processed_cols:
                    continue

                # 获取列值
                value = row[col]

                # 跳过空值
                if pd.isna(value):
                    continue

                # 处理不同类型的值
                if isinstance(value, (int, float)) and not pd.isna(value):
                    # 对于数值类型，保持原始类型
                    field_value = value
                else:
                    # 对于其他类型，转换为字符串
                    field_value = str(value).strip()

                    # 如果是空字符串，跳过
                    if not field_value:
                        continue

                # 将列名转换为合适的字段名（去除空格，转为小写）
                field_name = str(col).strip().lower().replace(" ", "_")

                # 添加到元数据和请求数据中
                metadata[field_name] = field_value
                request_data[field_name] = field_value

                print(f"[动态字段] {field_name}: {field_value}")

            # 发送请求
            try:
                t1 = time.time()
                response = requests.post(API_URL, json=request_data)
                t2 = time.time()
                print(f"请求耗时: {t2 - t1:.2f}秒")

                # 打印响应
                print(f"状态码: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    print(f"成功上传文本")
                    print(f"文档ID: {result.get('doc_id', '')}")
                    success_count += 1
                else:
                    print(f"错误响应: {response.text}")
                    error_count += 1
            except Exception as e:
                print(f"请求出错: {e}")
                error_count += 1

            # 显示进度
            progress = (index + 1) / total_rows * 100
            print(f"当前进度: {progress:.1f}%")
            # break


        # 打印最终统计信息
        print(f"\n所有数据处理完成!")
        print(f"成功上传: {success_count} 条")
        print(f"失败数量: {error_count} 条")

    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(upload_excel_to_milvus())
