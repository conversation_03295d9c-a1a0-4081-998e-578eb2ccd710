#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus数据迁移配置示例文件

复制此文件为 migration_config.py 并根据实际情况修改配置
"""

# 源数据库配置（当前使用的数据库）
# 如果留空，将从环境变量 VECTOR_DB_URI, VECTOR_DB_TOKEN, VECTOR_DB_DATABASE 读取
SOURCE_CONFIG = {
    "uri": "",  # 可选：源Milvus的URI，留空则从环境变量读取
    "token": "",  # 可选：源Milvus的Token，留空则从环境变量读取
    "database": "",  # 可选：源数据库名称，留空则从环境变量读取
}

# 目标数据库配置（新的Milvus数据库）
# ⚠️ 必须填写：这是数据迁移的目标位置
TARGET_CONFIG = {
    # 示例配置，请根据实际情况修改：
    
    # 本地Milvus示例
    # "uri": "http://localhost:19530",
    # "token": "",
    # "database": "default",
    
    # 云端Milvus示例
    # "uri": "https://your-cloud-milvus.com:19530",
    # "token": "your-cloud-token",
    # "database": "production",
    
    # Zilliz Cloud示例
    # "uri": "https://your-cluster.zillizcloud.com:19530",
    # "token": "your-zilliz-token",
    # "database": "default",
    
    # 请在下面填写您的实际配置：
    "uri": "",  # 必填：目标Milvus的URI
    "token": "",  # 可选：目标Milvus的Token（如果需要认证）
    "database": "",  # 可选：目标数据库名称（留空使用默认数据库）
}

# 迁移配置
MIGRATION_CONFIG = {
    "batch_size": 1000,  # 每批迁移的记录数，可根据内存和网络情况调整
    "verify_after_migration": True,  # 迁移后是否验证数据完整性
    "skip_existing_collections": True,  # 是否跳过已存在的集合
    "preserve_collection_names": True,  # 是否保持原集合名称
}

# 日志配置
LOGGING_CONFIG = {
    "log_level": "INFO",  # 日志级别: DEBUG, INFO, WARNING, ERROR
    "log_file": "migration.log",  # 日志文件名
    "console_output": True,  # 是否在控制台输出日志
}

# 常用配置模板
CONFIG_TEMPLATES = {
    "local_to_local": {
        "source": {
            "uri": "http://localhost:19530",
            "token": "",
            "database": "default"
        },
        "target": {
            "uri": "http://localhost:19531",  # 不同端口
            "token": "",
            "database": "backup"
        }
    },
    
    "local_to_cloud": {
        "source": {
            "uri": "http://localhost:19530",
            "token": "",
            "database": "default"
        },
        "target": {
            "uri": "https://your-cloud-milvus.com:19530",
            "token": "your-cloud-token",
            "database": "production"
        }
    },
    
    "cloud_to_cloud": {
        "source": {
            "uri": "https://source-cluster.zillizcloud.com:19530",
            "token": "source-token",
            "database": "default"
        },
        "target": {
            "uri": "https://target-cluster.zillizcloud.com:19530",
            "token": "target-token",
            "database": "default"
        }
    }
}

# 配置验证函数
def validate_config():
    """验证配置是否正确"""
    errors = []
    
    # 检查目标配置
    if not TARGET_CONFIG.get("uri"):
        errors.append("TARGET_CONFIG 中的 uri 不能为空")
    
    # 检查批次大小
    batch_size = MIGRATION_CONFIG.get("batch_size", 0)
    if not isinstance(batch_size, int) or batch_size <= 0:
        errors.append("MIGRATION_CONFIG 中的 batch_size 必须是正整数")
    
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("✅ 配置验证通过")
    return True

if __name__ == "__main__":
    print("=== 配置验证 ===")
    validate_config()
    
    print("\n=== 当前配置 ===")
    print(f"源数据库URI: {SOURCE_CONFIG.get('uri') or '从环境变量读取'}")
    print(f"目标数据库URI: {TARGET_CONFIG.get('uri') or '未配置'}")
    print(f"批次大小: {MIGRATION_CONFIG.get('batch_size')}")
    print(f"验证迁移: {MIGRATION_CONFIG.get('verify_after_migration')}")
