"""
加密工具模块，提供AES加密和解密功能
"""
import base64
import os
from typing import Union
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend

class AESEncryption:
    """
    AES加密工具类，提供AES-CBC模式的加密和解密功能
    """
    def __init__(self, key: Union[str, bytes] = None):
        """
        初始化AES加密工具

        Args:
            key: 加密密钥，如果不提供则自动生成一个随机密钥
        """
        if key is None:
            # 生成随机密钥
            self.key = os.urandom(32)  # 256位密钥
        elif isinstance(key, str):
            # 如果是字符串，转换为字节
            self.key = key.encode('utf-8')
            # 确保密钥长度为32字节（256位）
            self.key = self.key.ljust(32)[:32]
        else:
            # 确保密钥长度为32字节（256位）
            self.key = key.ljust(32)[:32]

    def encrypt(self, plaintext: Union[str, bytes]) -> str:
        """
        加密文本

        Args:
            plaintext: 要加密的文本，可以是字符串或字节

        Returns:
            加密后的Base64编码字符串
        """
        if isinstance(plaintext, str):
            plaintext = plaintext.encode('utf-8')

        # 生成随机IV
        iv = os.urandom(16)

        # 创建加密器
        cipher = Cipher(
            algorithms.AES(self.key),
            modes.CBC(iv),
            backend=default_backend()
        )
        encryptor = cipher.encryptor()

        # 添加PKCS7填充
        padder = padding.PKCS7(algorithms.AES.block_size).padder()
        padded_data = padder.update(plaintext) + padder.finalize()

        # 加密
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()

        # 将IV和密文拼接后进行Base64编码
        encrypted = base64.b64encode(iv + ciphertext).decode('utf-8')

        return encrypted

    def decrypt(self, encrypted: Union[str, bytes]) -> str:
        """
        解密文本

        Args:
            encrypted: 加密后的Base64编码字符串或字节

        Returns:
            解密后的文本
        """
        if isinstance(encrypted, str):
            encrypted = encrypted.encode('utf-8')

        # Base64解码
        decoded = base64.b64decode(encrypted)

        # 提取IV和密文
        iv = decoded[:16]
        ciphertext = decoded[16:]

        # 创建解密器
        cipher = Cipher(
            algorithms.AES(self.key),
            modes.CBC(iv),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()

        # 解密
        padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()

        # 去除PKCS7填充
        unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
        plaintext = unpadder.update(padded_plaintext) + unpadder.finalize()

        return plaintext.decode('utf-8')

# 默认加密器实例，使用配置文件中的密钥或默认密钥
_default_encryptor = None

def get_default_encryptor() -> AESEncryption:
    """
    获取默认的AES加密器实例

    Returns:
        AESEncryption实例
    """
    global _default_encryptor
    if _default_encryptor is None:
        # 从配置文件获取密钥
        try:
            from app.config.settings import EncryptionConfig
            config = EncryptionConfig()
            key = config.key
            print(f"[加密] 使用配置文件中的加密密钥")

            # 如果配置文件中没有密钥，则使用默认密钥
            if not key:
                key = "RAGServiceSecretKey2024"  # 默认密钥，与.env文件中的保持一致
                print(f"[加密] 配置文件中没有密钥，使用默认密钥")
        except Exception as e:
            print(f"[加密] 加载配置文件失败: {e}")
            # 使用默认密钥
            key = "RAGServiceSecretKey2024"  # 默认密钥，与.env文件中的保持一致
            print(f"[加密] 使用默认密钥")

        _default_encryptor = AESEncryption(key)
        print(f"[加密] 成功初始化AES加密器")
    return _default_encryptor

def encrypt_text(text: str) -> str:
    """
    使用默认加密器加密文本

    Args:
        text: 要加密的文本

    Returns:
        加密后的Base64编码字符串
    """
    return get_default_encryptor().encrypt(text)

def decrypt_text(encrypted: str) -> str:
    """
    使用默认加密器解密文本

    Args:
        encrypted: 加密后的Base64编码字符串

    Returns:
        解密后的文本
    """
    return get_default_encryptor().decrypt(encrypted)
