#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级Milvus向量数据库迁移工具
支持断点续传、进度监控、错误恢复等功能
"""

import os
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
from app.core.vectordb import <PERSON>lvusVectorDB
from migration_config import SOURCE_CONFIG, TARGET_CONFIG, MIGRATION_CONFIG, LOGGING_CONFIG

# 加载环境变量
load_dotenv()

class AdvancedMilvusMigrator:
    def __init__(self, config_file: Optional[str] = None):
        # 源数据库配置（优先使用配置文件，其次使用环境变量）
        self.source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
        self.source_token = SOURCE_CONFIG.get("token") or os.getenv("VECTOR_DB_TOKEN", "")
        self.source_database = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")

        # 目标数据库配置（从配置文件读取）
        self.target_uri = TARGET_CONFIG.get("uri", "")
        self.target_token = TARGET_CONFIG.get("token", "")
        self.target_database = TARGET_CONFIG.get("database", "")
        
        # 迁移配置
        self.batch_size = MIGRATION_CONFIG.get("batch_size", 1000)
        self.verify_after_migration = MIGRATION_CONFIG.get("verify_after_migration", True)
        self.skip_existing = MIGRATION_CONFIG.get("skip_existing_collections", True)
        
        # 数据库连接
        self.source_db = None
        self.target_db = None
        
        # 进度跟踪
        self.progress_file = "migration_progress.json"
        self.progress_data = {}
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        log_level = getattr(logging, LOGGING_CONFIG.get("log_level", "INFO"))
        log_file = LOGGING_CONFIG.get("log_file", "migration.log")
        
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 设置根日志记录器
        self.logger = logging.getLogger('MilvusMigrator')
        self.logger.setLevel(log_level)
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 控制台处理器
        if LOGGING_CONFIG.get("console_output", True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
    
    def load_progress(self):
        """加载迁移进度"""
        if Path(self.progress_file).exists():
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    self.progress_data = json.load(f)
                self.logger.info(f"已加载迁移进度: {len(self.progress_data)} 个集合")
            except Exception as e:
                self.logger.warning(f"加载进度文件失败: {e}")
                self.progress_data = {}
        else:
            self.progress_data = {}
    
    def save_progress(self):
        """保存迁移进度"""
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存进度文件失败: {e}")
    
    async def connect_databases(self) -> bool:
        """连接源数据库和目标数据库"""
        self.logger.info("开始连接数据库...")
        
        try:
            # 连接源数据库
            self.logger.info(f"连接源数据库: {self.source_uri}")
            self.source_db = MilvusVectorDB(
                uri=self.source_uri, 
                token=self.source_token, 
                database=self.source_database
            )
            await self.source_db.connect()
            self.logger.info("源数据库连接成功")
            
            # 检查目标数据库配置
            if not self.target_uri:
                self.logger.error("目标数据库URI未配置")
                return False
                
            # 连接目标数据库
            self.logger.info(f"连接目标数据库: {self.target_uri}")
            self.target_db = MilvusVectorDB(
                uri=self.target_uri, 
                token=self.target_token, 
                database=self.target_database
            )
            await self.target_db.connect()
            self.logger.info("目标数据库连接成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    async def get_collections_to_migrate(self) -> List[str]:
        """获取需要迁移的集合列表"""
        await self.source_db._refresh_collections()
        all_collections = list(self.source_db.collections_cache)
        
        if not self.skip_existing:
            return all_collections
        
        # 检查目标数据库中已存在的集合
        await self.target_db._refresh_collections()
        existing_collections = set(self.target_db.collections_cache)
        
        # 过滤掉已存在的集合
        collections_to_migrate = [
            col for col in all_collections 
            if col not in existing_collections
        ]
        
        if existing_collections:
            self.logger.info(f"跳过已存在的集合: {list(existing_collections)}")
        
        return collections_to_migrate

    async def get_collection_vector_dimension(self, collection_name: str) -> Optional[int]:
        """获取集合的向量维度"""
        try:
            # 方法1: 通过describe_collection获取schema信息
            try:
                collection_info = self.source_db.client.describe_collection(collection_name)
                if 'fields' in collection_info:
                    for field in collection_info['fields']:
                        if field.get('type') == 'FloatVector' or field.get('name') == 'vector':
                            if 'params' in field and 'dim' in field['params']:
                                dim = field['params']['dim']
                                self.logger.info(f"通过schema获取到向量维度: {dim}")
                                return dim
            except Exception as e:
                self.logger.warning(f"通过schema获取向量维度失败: {e}")

            # 方法2: 通过查询少量数据获取向量维度
            try:
                query_results = self.source_db.client.query(
                    collection_name=collection_name,
                    filter="",
                    output_fields=["vector"],
                    limit=1
                )

                if query_results and len(query_results) > 0:
                    vector = query_results[0].get('vector')
                    if vector and isinstance(vector, list):
                        dim = len(vector)
                        self.logger.info(f"通过查询数据获取到向量维度: {dim}")
                        return dim
            except Exception as e:
                self.logger.warning(f"通过查询数据获取向量维度失败: {e}")

            # 方法3: 让用户手动输入
            self.logger.warning(f"无法自动获取集合 {collection_name} 的向量维度")
            print(f"\n⚠️ 无法自动获取集合 '{collection_name}' 的向量维度")
            print("请手动输入向量维度（常见维度: 384, 768, 1536, 3072, 12288）:")

            while True:
                try:
                    dim_input = input("向量维度: ").strip()
                    if dim_input:
                        dim = int(dim_input)
                        if dim > 0:
                            self.logger.info(f"用户输入向量维度: {dim}")
                            return dim
                        else:
                            print("❌ 向量维度必须是正整数")
                    else:
                        print("❌ 请输入向量维度")
                except ValueError:
                    print("❌ 请输入有效的数字")
                except KeyboardInterrupt:
                    self.logger.error("用户取消输入")
                    return None

        except Exception as e:
            self.logger.error(f"获取向量维度时发生错误: {e}")
            return None

    async def migrate_collection_with_resume(self, collection_name: str) -> bool:
        """支持断点续传的集合迁移"""
        self.logger.info(f"开始迁移集合: {collection_name}")
        
        # 检查进度
        collection_progress = self.progress_data.get(collection_name, {
            "status": "pending",
            "migrated_count": 0,
            "total_count": 0,
            "last_offset": 0,
            "start_time": None,
            "end_time": None
        })
        
        try:
            # 获取集合信息
            stats = self.source_db.client.get_collection_stats(collection_name)
            total_count = stats.get('row_count', 0)
            
            if total_count == 0:
                self.logger.warning(f"集合 {collection_name} 为空，跳过迁移")
                collection_progress["status"] = "completed"
                self.progress_data[collection_name] = collection_progress
                self.save_progress()
                return True
            
            # 更新总数
            collection_progress["total_count"] = total_count
            
            # 如果是新开始的迁移
            if collection_progress["status"] == "pending":
                collection_progress["start_time"] = datetime.now().isoformat()
                collection_progress["status"] = "in_progress"
                
                # 获取向量维度并创建目标集合
                vector_dim = await self.get_collection_vector_dimension(collection_name)
                if vector_dim is None:
                    self.logger.error(f"无法获取集合 {collection_name} 的向量维度")
                    return False
                await self.target_db.create_collection(collection_name, vector_dim)
                self.logger.info(f"创建目标集合 {collection_name}，维度: {vector_dim}")
            
            # 从上次中断的位置继续
            start_offset = collection_progress["last_offset"]
            migrated_count = collection_progress["migrated_count"]
            
            self.logger.info(f"从偏移量 {start_offset} 继续迁移，已迁移 {migrated_count}/{total_count}")
            
            # 分批迁移
            current_offset = start_offset
            while current_offset < total_count:
                batch_size = min(self.batch_size, total_count - current_offset)
                
                # 获取批量数据
                batch_data = await self.get_batch_data(collection_name, current_offset, batch_size)
                
                if not batch_data:
                    self.logger.warning(f"偏移量 {current_offset} 处获取数据为空")
                    break
                
                # 插入数据
                success = await self.insert_batch_data(collection_name, batch_data)
                
                if success:
                    current_offset += len(batch_data)
                    migrated_count += len(batch_data)
                    
                    # 更新进度
                    collection_progress["last_offset"] = current_offset
                    collection_progress["migrated_count"] = migrated_count
                    self.progress_data[collection_name] = collection_progress
                    self.save_progress()
                    
                    # 显示进度
                    progress_percent = (migrated_count / total_count) * 100
                    self.logger.info(f"集合 {collection_name} 进度: {migrated_count}/{total_count} ({progress_percent:.1f}%)")
                    
                else:
                    self.logger.error(f"批次迁移失败，偏移量: {current_offset}")
                    return False
            
            # 迁移完成
            collection_progress["status"] = "completed"
            collection_progress["end_time"] = datetime.now().isoformat()
            collection_progress["migrated_count"] = migrated_count
            self.progress_data[collection_name] = collection_progress
            self.save_progress()
            
            self.logger.info(f"集合 {collection_name} 迁移完成，总计: {migrated_count} 条记录")
            
            # 验证迁移结果
            if self.verify_after_migration:
                return await self.verify_migration(collection_name)
            
            return True
            
        except Exception as e:
            self.logger.error(f"迁移集合 {collection_name} 失败: {e}")
            collection_progress["status"] = "failed"
            collection_progress["error"] = str(e)
            self.progress_data[collection_name] = collection_progress
            self.save_progress()
            return False
    
    async def get_batch_data(self, collection_name: str, offset: int, limit: int) -> List[Dict[str, Any]]:
        """获取批量数据"""
        try:
            results = self.source_db.client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["*"],
                offset=offset,
                limit=limit
            )
            return results
        except Exception as e:
            self.logger.error(f"获取批量数据失败: {e}")
            return []
    
    async def insert_batch_data(self, collection_name: str, data: List[Dict[str, Any]]) -> bool:
        """插入批量数据"""
        try:
            # 数据格式转换
            records = []
            dynamic_fields_found = set()

            for item in data:
                # 创建新的记录副本，避免修改原始数据
                record = {}

                # 处理标准字段
                for key, value in item.items():
                    if key == 'id':
                        # 跳过自动生成的id字段
                        continue
                    elif key == 'metadata':
                        # 确保metadata是字典格式
                        if isinstance(value, str):
                            try:
                                record['metadata'] = json.loads(value)
                                self.logger.info(f"[兼容性] 成功解析字符串格式的metadata")
                            except Exception as e:
                                self.logger.warning(f"[兼容性警告] 无法解析metadata字符串: {e}")
                                record['metadata'] = {}
                        else:
                            record['metadata'] = value if isinstance(value, dict) else {}
                    else:
                        # 保留所有其他字段（包括动态字段）
                        record[key] = value

                        # 记录动态字段
                        if key not in ['vector', 'content', 'collection_name', 'metadata']:
                            dynamic_fields_found.add(key)

                records.append(record)

            # 输出动态字段信息
            if dynamic_fields_found:
                self.logger.info(f"[动态字段] 发现动态字段: {list(dynamic_fields_found)}")

            await self.target_db.insert_vectors(collection_name, records)
            return True

        except Exception as e:
            self.logger.error(f"插入批量数据失败: {e}")
            return False
    
    async def verify_migration(self, collection_name: str) -> bool:
        """验证迁移结果"""
        try:
            source_stats = self.source_db.client.get_collection_stats(collection_name)
            target_stats = self.target_db.client.get_collection_stats(collection_name)
            
            source_count = source_stats.get('row_count', 0)
            target_count = target_stats.get('row_count', 0)
            
            if source_count == target_count:
                self.logger.info(f"验证成功: {collection_name} ({source_count} 条记录)")
                return True
            else:
                self.logger.error(f"验证失败: {collection_name} 源:{source_count} 目标:{target_count}")
                return False
                
        except Exception as e:
            self.logger.error(f"验证迁移结果失败: {e}")
            return False
    
    async def run_migration(self, selected_collections: List[str] = None):
        """运行迁移任务"""
        self.logger.info("开始高级迁移任务")
        
        # 加载进度
        self.load_progress()
        
        # 连接数据库
        if not await self.connect_databases():
            self.logger.error("数据库连接失败，退出迁移")
            return
        
        # 获取要迁移的集合
        if selected_collections:
            collections = selected_collections
        else:
            collections = await self.get_collections_to_migrate()
        
        if not collections:
            self.logger.info("没有需要迁移的集合")
            return
        
        self.logger.info(f"将迁移 {len(collections)} 个集合: {collections}")
        
        # 开始迁移
        success_count = 0
        failed_collections = []
        
        start_time = time.time()
        
        for i, collection in enumerate(collections, 1):
            self.logger.info(f"[{i}/{len(collections)}] 处理集合: {collection}")
            
            success = await self.migrate_collection_with_resume(collection)
            
            if success:
                success_count += 1
            else:
                failed_collections.append(collection)
        
        # 迁移总结
        end_time = time.time()
        duration = end_time - start_time
        
        self.logger.info("=" * 50)
        self.logger.info("迁移任务完成")
        self.logger.info(f"总耗时: {duration:.2f} 秒")
        self.logger.info(f"成功迁移: {success_count} 个集合")
        self.logger.info(f"失败集合: {len(failed_collections)} 个")
        
        if failed_collections:
            self.logger.error(f"失败的集合: {failed_collections}")
    
    def show_progress_summary(self):
        """显示迁移进度摘要"""
        if not self.progress_data:
            print("没有迁移进度数据")
            return
        
        print("\n=== 迁移进度摘要 ===")
        for collection, progress in self.progress_data.items():
            status = progress.get("status", "unknown")
            migrated = progress.get("migrated_count", 0)
            total = progress.get("total_count", 0)
            
            if total > 0:
                percent = (migrated / total) * 100
                print(f"{collection}: {status} - {migrated}/{total} ({percent:.1f}%)")
            else:
                print(f"{collection}: {status}")

async def main():
    """主函数"""
    print("🚀 高级Milvus向量数据库迁移工具")
    print("=" * 50)
    
    migrator = AdvancedMilvusMigrator()
    
    # 显示当前进度
    migrator.load_progress()
    migrator.show_progress_summary()
    
    print("\n选择操作:")
    print("1. 开始/继续迁移所有集合")
    print("2. 迁移指定集合")
    print("3. 查看进度摘要")
    print("4. 清除进度记录")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        await migrator.run_migration()
    elif choice == "2":
        # 获取集合列表供选择
        if await migrator.connect_databases():
            await migrator.source_db._refresh_collections()
            collections = list(migrator.source_db.collections_cache)
            
            print("\n可用集合:")
            for i, col in enumerate(collections, 1):
                print(f"  {i}. {col}")
            
            selection = input("请输入集合序号 (用逗号分隔): ").strip()
            try:
                indices = [int(x.strip()) - 1 for x in selection.split(',')]
                selected = [collections[i] for i in indices if 0 <= i < len(collections)]
                await migrator.run_migration(selected)
            except:
                print("输入格式错误")
    elif choice == "3":
        migrator.show_progress_summary()
    elif choice == "4":
        confirm = input("确认清除所有进度记录? (y/N): ").strip().lower()
        if confirm == 'y':
            if Path(migrator.progress_file).exists():
                Path(migrator.progress_file).unlink()
            print("进度记录已清除")
    else:
        print("无效选择")

if __name__ == "__main__":
    asyncio.run(main())
