# 业务逻辑层 (Services)

## 概述

服务层是RAG系统分层架构中的业务逻辑层，负责封装复杂的业务逻辑，协调多个Repository的操作，为上层Controller提供高级API接口。

## 架构设计

### 设计原则

1. **业务逻辑封装**: 将复杂的业务流程封装在服务层，避免在Controller中出现复杂逻辑
2. **Repository协调**: 协调多个Repository的操作，实现跨模块的业务流程
3. **错误处理**: 统一的错误处理和异常传播机制
4. **性能优化**: 集成缓存、批量操作、并发处理等性能优化策略
5. **可扩展性**: 为新的业务需求提供扩展接口

### 层次关系

```
Routes Layer (API接口层)
    ↓
Services Layer (业务逻辑层) ← 当前层
    ↓
Repositories Layer (数据访问层)
    ↓
Core Layer (核心功能层)
    ↓
Utils Layer (工具层)
```

## 服务组件

### 1. DatabaseService (数据库管理服务)

**职责**: 数据库和集合的生命周期管理

**核心功能**:
- 数据库服务初始化和配置
- 数据库连接管理和复用
- 集合创建和管理
- 混合检索集合创建

**关键方法**:
```python
async def initialize(config: VectorDBConfig) -> Dict[str, Any]
async def create_collection(collection_name: str, vector_dimension: int, ...) -> Dict[str, Any]
async def create_hybrid_collection(collection_name: str, dense_vector_dim: int, ...) -> Dict[str, Any]
async def get_database_connection(database: str = None, create_if_not_exists: bool = False) -> Any
```

**使用示例**:
```python
from app.services import DatabaseService
from app.config.settings import VectorDBConfig

# 初始化服务
db_service = DatabaseService()
config = VectorDBConfig(db_type="milvus", host="localhost", port=19530)
await db_service.initialize(config)

# 创建集合
result = await db_service.create_collection(
    collection_name="my_collection",
    vector_dimension=768
)
```

### 2. VectorService (向量操作服务)

**职责**: 向量数据的增删改查操作

**核心功能**:
- 向量数据插入和批量插入
- Upsert操作（插入或更新）
- 向量数据删除
- 并发向量处理
- 向量数据统计

**关键方法**:
```python
async def insert_vectors(collection_name: str, texts: List[str], ...) -> Dict[str, Any]
async def batch_insert_vectors(collection_name: str, texts: List[str], batch_size: int, ...) -> Dict[str, Any]
async def upsert_vectors(collection_name: str, texts: List[str], ...) -> Dict[str, Any]
async def delete_vectors(collection_name: str, filter_expr: str = None, ...) -> Dict[str, Any]
```

**特色功能**:
- **自动向量化**: 自动将文本转换为向量
- **批量处理**: 支持大量数据的批量插入
- **性能监控**: 详细的操作时间统计
- **错误恢复**: 批量操作中的错误处理和恢复

### 3. SearchService (搜索服务)

**职责**: 向量搜索和高级搜索功能

**核心功能**:
- 基本向量搜索
- 多集合搜索
- 分页搜索
- 带筛选条件的搜索
- 相似文档搜索

**关键方法**:
```python
async def search_vectors(collection_name: str, query: str, top_k: int, ...) -> Dict[str, Any]
async def search_multiple_collections(collections: List[str], query: str, ...) -> Dict[str, Any]
async def search_with_pagination(collection_name: str, query: str, page: int, ...) -> Dict[str, Any]
async def search_similar_documents(collection_name: str, document_id: str, ...) -> Dict[str, Any]
```

**高级特性**:
- **智能查询**: 自动优化查询向量生成
- **结果聚合**: 多集合搜索结果的智能合并和排序
- **筛选集成**: 复杂筛选条件的构建和应用
- **性能分析**: 搜索性能的详细分析

### 4. HybridSearchService (混合检索服务)

**职责**: 语义搜索和全文搜索的混合检索

**核心功能**:
- 混合检索（语义+全文）
- 纯语义检索
- 纯全文检索
- 多集合混合检索
- 可配置的检索策略

**关键方法**:
```python
async def hybrid_search(collection_name: str, query: str, dense_weight: float, sparse_weight: float, ...) -> Dict[str, Any]
async def semantic_only_search(collection_name: str, query: str, ...) -> Dict[str, Any]
async def full_text_only_search(collection_name: str, query: str, ...) -> Dict[str, Any]
async def multi_hybrid_search(collections: List[str], query: str, ...) -> Dict[str, Any]
```

**检索策略**:
- **RRF (Reciprocal Rank Fusion)**: 倒数排名融合
- **加权平均**: 基于权重的分数融合
- **重排序**: 多种重排序算法支持

### 5. DocumentService (文档处理服务)

**职责**: 文档上传、解析、分块和向量化

**核心功能**:
- 文档上传和解析
- 文本分块和预处理
- 批量文档处理
- 向量化集成

**关键方法**:
```python
async def upload_texts(texts: List[str], collection_name: str, ...) -> Dict[str, Any]
```

**处理流程**:
1. 文档解析和文本提取
2. 文本清理和预处理
3. 智能分块处理
4. 向量生成和存储
5. 元数据管理

## 服务集成模式

### 1. 服务组合模式

多个服务协作完成复杂业务流程:

```python
# 完整的文档处理流程
async def complete_document_workflow(file, collection_name):
    # 1. 初始化所有服务
    db_service = DatabaseService()
    doc_service = DocumentService()
    
    await db_service.initialize(config)
    await doc_service.initialize(config)
    
    # 2. 创建集合（如果不存在）
    await db_service.create_collection(collection_name, 768)
    
    # 3. 上传和处理文档
    result = await doc_service.upload_document(file, collection_name)
    
    return result
```

### 2. 服务链模式

服务之间的顺序调用:

```python
# 搜索增强流程
async def enhanced_search_workflow(query, collections):
    search_service = SearchService()
    hybrid_service = HybridSearchService()
    
    # 1. 基础搜索
    basic_results = await search_service.search_multiple_collections(collections, query)
    
    # 2. 混合检索增强
    hybrid_results = await hybrid_service.multi_hybrid_search(collections, query)
    
    # 3. 结果融合和优化
    return merge_and_optimize_results(basic_results, hybrid_results)
```

## 错误处理策略

### 1. 分层错误处理

```python
class ServiceError(Exception):
    """服务层基础异常"""
    pass

class DatabaseServiceError(ServiceError):
    """数据库服务异常"""
    pass

class VectorServiceError(ServiceError):
    """向量服务异常"""
    pass
```

### 2. 优雅降级

```python
async def robust_search(query, collections):
    try:
        # 尝试混合检索
        return await hybrid_search_service.multi_hybrid_search(collections, query)
    except HybridSearchError:
        # 降级到基础搜索
        return await search_service.search_multiple_collections(collections, query)
    except SearchServiceError:
        # 最终降级到单集合搜索
        return await search_service.search_vectors(collections[0], query)
```

## 性能优化特性

### 1. 批量处理

```python
# 批量向量插入
await vector_service.batch_insert_vectors(
    collection_name="large_collection",
    texts=large_text_list,
    batch_size=100  # 每批100条记录
)
```

### 2. 并发处理

```python
# 并发多集合搜索
async def concurrent_multi_search(query, collections):
    tasks = []
    for collection in collections:
        task = search_service.search_vectors(collection, query)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return merge_results(results)
```

### 3. 连接复用

所有服务共享数据库连接池，避免重复连接创建。

## 监控和日志

### 1. 操作日志

每个服务都集成了详细的操作日志:

```python
self.operation_logger.log_operation_success(
    operation_name="向量插入",
    collection=collection_name,
    count=len(texts),
    total_time=f"{elapsed_time:.3f}秒"
)
```

### 2. 性能监控

```python
timer = TimeTracker("混合检索")
timer.start()

# 业务逻辑
vector_gen_time = timer.checkpoint("查询向量生成")
search_time = timer.checkpoint("检索执行")
elapsed_time = timer.finish()
```

### 3. 状态监控

```python
# 获取服务状态
status = service.get_service_status()
# 返回: {"initialized": True, "components": {...}}
```

## 扩展指南

### 1. 添加新服务

1. 创建新的服务类，继承基础结构
2. 实现必要的初始化和业务方法
3. 添加错误处理和日志记录
4. 更新`__init__.py`导出

### 2. 扩展现有服务

1. 添加新的业务方法
2. 保持向后兼容性
3. 更新文档和测试

### 3. 集成新的Repository

```python
class NewService:
    def __init__(self):
        self.db_repo = VectorDatabaseRepository()
        self.new_repo: Optional[NewRepository] = None
    
    async def initialize(self, config):
        db_connection = await self.db_repo.get_database_connection()
        self.new_repo = NewRepository(db_connection)
```

## 最佳实践

### 1. 服务初始化

```python
# 推荐：使用配置对象
config = VectorDBConfig.from_env()
await service.initialize(config)

# 避免：直接传递参数
```

### 2. 错误处理

```python
# 推荐：具体的异常处理
try:
    result = await service.operation()
except VectorServiceError as e:
    logger.error(f"向量操作失败: {e}")
    return error_response(str(e))
except Exception as e:
    logger.error(f"未知错误: {e}")
    return error_response("系统错误")
```

### 3. 资源管理

```python
# 推荐：使用上下文管理器或finally块
try:
    await service.initialize(config)
    # 业务逻辑
finally:
    service.close_connections()
```

## 与其他层的交互

### 1. 从Routes层调用

```python
# routes.py
@app.post("/upload")
async def upload_document(file: UploadFile, collection: str):
    doc_service = DocumentService()
    await doc_service.initialize(config)
    
    try:
        result = await doc_service.upload_document(file, collection)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 2. 调用Repository层

```python
# 在服务内部
async def complex_operation(self):
    # 协调多个repository
    vector_result = await self.vector_repo.insert_vectors(...)
    search_result = await self.search_repo.search_vectors(...)
    
    # 业务逻辑处理
    return combine_results(vector_result, search_result)
```

## 总结

服务层是RAG系统的核心业务逻辑层，通过五个专门的服务类提供了完整的业务功能：

1. **DatabaseService**: 数据库和集合管理的中央控制点
2. **VectorService**: 向量数据操作的专业服务
3. **SearchService**: 智能搜索功能的核心实现
4. **HybridSearchService**: 先进混合检索技术的封装
5. **DocumentService**: 文档处理流程的完整解决方案

这一层的设计遵循了单一职责、开放封闭、依赖注入等SOLID原则，为上层提供了清晰、易用、可扩展的业务API接口，大大提升了系统的可维护性和可测试性。 