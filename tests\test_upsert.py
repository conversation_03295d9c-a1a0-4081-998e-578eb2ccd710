#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版 Upsert 接口调用示例

展示最常用的upsert使用场景：不传向量，自动生成
"""

import requests
import json

# 配置
API_URL = "http://copilot.csvw.com/rag_service/api/v1/upsert"


def upsert_update_existing():
    """更新已存在的记录"""
    
    print("\n🔄 更新已存在的记录")
    
    data = {
        "records": [
            {
                "id": 458603706594,  # 使用相同ID更新
                "content": "这是更新后的第一个文档内容，增加了更多关于AI的最新发展。",
                "metadata": {
                    "title": "AI发展史 - 2024更新版",
                    "category": "technology",
                    "updated": "2024-01-15",
                    "version": 2
                }
            }
        ],
        "collection": "test4",
        "database": "lkz_test_V1",
        "embedding_type": "qwen3"
    }
    
    try:
        response = requests.post(API_URL, json=data, timeout=60)
        if response.status_code == 200:
            result = response.json()
            print("✅ 更新成功!")
            print(f"更新记录数: {result.get('upsert_count', 0)}")
        else:
            print(f"❌ 更新失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 更新异常: {e}")

if __name__ == "__main__":
    print("📚 Upsert接口简单示例")
    print("🎯 特点：无需手动传入向量，系统自动生成")
    print("-" * 50)
    
    # 示例2：更新记录
    upsert_update_existing()
    
    print("\n" + "="*50)
    print("💡 关键要点:")
    print("• 只需要提供 id 和 content/text 字段")
    print("• 系统会自动生成向量") 
    print("• 相同ID会更新，不同ID会插入")
    print("• 支持metadata存储额外信息")
    print("• auto_flush=True 确保立即生效")
    print("="*50)