"""
响应工具模块
提供统一的响应处理功能
"""
from typing import Dict, Any, List, Optional
from fastapi import HTTPException


class ResponseBuilder:
    """响应构建器"""
    
    @staticmethod
    def success_response(
        data: Any = None,
        message: str = "操作成功",
        code: int = 200,
        extra_fields: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        构建成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            code: 状态码
            extra_fields: 额外字段
            
        Returns:
            Dict: 响应字典
        """
        response = {
            "success": True,
            "code": code,
            "message": message
        }
        
        if data is not None:
            response["data"] = data
        
        if extra_fields:
            response.update(extra_fields)
        
        return response
    
    @staticmethod
    def error_response(
        message: str = "操作失败",
        code: int = 400,
        error_code: Optional[str] = None,
        details: Optional[Any] = None,
        extra_fields: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        构建错误响应
        
        Args:
            message: 错误消息
            code: 状态码
            error_code: 错误代码
            details: 错误详情
            extra_fields: 额外字段
            
        Returns:
            Dict: 响应字典
        """
        response = {
            "success": False,
            "code": code,
            "message": message
        }
        
        if error_code:
            response["error_code"] = error_code
        
        if details is not None:
            response["details"] = details
        
        if extra_fields:
            response.update(extra_fields)
        
        return response
    
    @staticmethod
    def paginated_response(
        items: List[Any],
        total: int,
        page: int = 1,
        page_size: int = 10,
        message: str = "查询成功",
        extra_fields: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        构建分页响应
        
        Args:
            items: 数据项列表
            total: 总数量
            page: 当前页码
            page_size: 每页大小
            message: 响应消息
            extra_fields: 额外字段
            
        Returns:
            Dict: 分页响应字典
        """
        total_pages = (total + page_size - 1) // page_size if page_size > 0 else 1
        
        response = {
            "success": True,
            "code": 200,
            "message": message,
            "data": {
                "items": items,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_items": total,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
            }
        }
        
        if extra_fields:
            response.update(extra_fields)
        
        return response


class UploadResponseBuilder:
    """上传响应构建器"""
    
    @staticmethod
    def document_upload_response(
        doc_id: str,
        chunk_count: int,
        vector_dimension: int,
        processing_time: str,
        message: str = "文档上传成功"
    ) -> Dict[str, Any]:
        """
        构建文档上传响应
        
        Args:
            doc_id: 文档ID
            chunk_count: 分块数量
            vector_dimension: 向量维度
            processing_time: 处理时间
            message: 响应消息
            
        Returns:
            Dict: 响应字典
        """
        return ResponseBuilder.success_response(
            data={
                "doc_id": doc_id,
                "chunk_count": chunk_count,
                "vector_dimension": vector_dimension
            },
            message=message,
            extra_fields={
                "processing_time": processing_time
            }
        )
    
    @staticmethod
    def text_upload_response(
        doc_id: str,
        text_count: int,
        vector_dimension: int,
        processing_time: str,
        encrypted: bool = False,
        message: str = "文本上传成功"
    ) -> Dict[str, Any]:
        """
        构建文本上传响应
        
        Args:
            doc_id: 文档ID
            text_count: 文本数量
            vector_dimension: 向量维度
            processing_time: 处理时间
            encrypted: 是否加密
            message: 响应消息
            
        Returns:
            Dict: 响应字典
        """
        return ResponseBuilder.success_response(
            data={
                "doc_id": doc_id,
                "text_count": text_count,
                "vector_dimension": vector_dimension,
                "encrypted": encrypted
            },
            message=message,
            extra_fields={
                "processing_time": processing_time
            }
        )
    
    @staticmethod
    def batch_upload_response(
        doc_id: str,
        text_count: int,
        batch_count: int,
        batch_size: int,
        vector_dimension: int,
        processing_time: str,
        avg_time_per_text: str,
        encrypted: bool = False,
        message: str = "批量上传成功"
    ) -> Dict[str, Any]:
        """
        构建批量上传响应
        
        Args:
            doc_id: 文档ID
            text_count: 文本数量
            batch_count: 批次数量
            batch_size: 批次大小
            vector_dimension: 向量维度
            processing_time: 总处理时间
            avg_time_per_text: 平均每条文本处理时间
            encrypted: 是否加密
            message: 响应消息
            
        Returns:
            Dict: 响应字典
        """
        return ResponseBuilder.success_response(
            data={
                "doc_id": doc_id,
                "text_count": text_count,
                "batch_count": batch_count,
                "batch_size": batch_size,
                "vector_dimension": vector_dimension,
                "encrypted": encrypted
            },
            message=message,
            extra_fields={
                "processing_time": processing_time,
                "avg_time_per_text": avg_time_per_text
            }
        )


class SearchResponseBuilder:
    """搜索响应构建器"""
    
    @staticmethod
    def search_response(
        query: str,
        results: List[Dict[str, Any]],
        search_time: str,
        collection: Optional[str] = None,
        database: Optional[str] = None,
        time_details: Optional[Dict[str, str]] = None,
        message: str = "搜索完成"
    ) -> Dict[str, Any]:
        """
        构建搜索响应
        
        Args:
            query: 查询文本
            results: 搜索结果
            search_time: 搜索时间
            collection: 集合名称
            database: 数据库名称
            time_details: 时间详情
            message: 响应消息
            
        Returns:
            Dict: 响应字典
        """
        data = {
            "query": query,
            "results": results,
            "total_results": len(results)
        }
        
        if collection:
            data["collection"] = collection
        
        if database:
            data["database"] = database
        
        extra_fields = {"search_time": search_time}
        
        if time_details:
            extra_fields["time_details"] = time_details
        
        return ResponseBuilder.success_response(
            data=data,
            message=message,
            extra_fields=extra_fields
        )
    
    @staticmethod
    def multi_search_response(
        query: str,
        results: List[Dict[str, Any]],
        search_time: str,
        databases_searched: int,
        message: str = "多数据库搜索完成"
    ) -> Dict[str, Any]:
        """
        构建多数据库搜索响应
        
        Args:
            query: 查询文本
            results: 搜索结果
            search_time: 搜索时间
            databases_searched: 搜索的数据库数量
            message: 响应消息
            
        Returns:
            Dict: 响应字典
        """
        return ResponseBuilder.success_response(
            data={
                "query": query,
                "results": results,
                "total_results": len(results),
                "databases_searched": databases_searched
            },
            message=message,
            extra_fields={
                "search_time": search_time
            }
        )
    
    @staticmethod
    def hybrid_search_response(
        query: str,
        results: List[Dict[str, Any]],
        search_time: str,
        search_strategy: str,
        rerank_strategy: Optional[str] = None,
        databases_searched: int = 1,
        errors: Optional[List[Dict[str, Any]]] = None,
        message: str = "混合检索完成"
    ) -> Dict[str, Any]:
        """
        构建混合检索响应
        
        Args:
            query: 查询文本
            results: 搜索结果
            search_time: 搜索时间
            search_strategy: 搜索策略
            rerank_strategy: 重排序策略
            databases_searched: 搜索的数据库数量
            errors: 错误列表
            message: 响应消息
            
        Returns:
            Dict: 响应字典
        """
        data = {
            "query": query,
            "results": results,
            "total_results": len(results),
            "search_strategy": search_strategy,
            "databases_searched": databases_searched
        }
        
        if rerank_strategy:
            data["rerank_strategy"] = rerank_strategy
        
        extra_fields = {"search_time": search_time}
        
        if errors:
            extra_fields["errors"] = errors
            extra_fields["error_count"] = len(errors)
        
        return ResponseBuilder.success_response(
            data=data,
            message=message,
            extra_fields=extra_fields
        )


class VectorOperationResponseBuilder:
    """向量操作响应构建器"""
    
    @staticmethod
    def upsert_response(
        upsert_count: int,
        collection: str,
        database: Optional[str] = None,
        processing_time: str = "",
        vector_generation_time: str = "",
        auto_generated_vectors: int = 0,
        manual_vectors: int = 0,
        vector_dimension: Optional[int] = None,
        encrypted: bool = False,
        auto_flush: bool = True,
        message: str = "Upsert操作成功"
    ) -> Dict[str, Any]:
        """
        构建Upsert响应
        
        Args:
            upsert_count: 更新/插入的记录数
            collection: 集合名称
            database: 数据库名称
            processing_time: 处理时间
            vector_generation_time: 向量生成时间
            auto_generated_vectors: 自动生成向量数量
            manual_vectors: 手动提供向量数量
            vector_dimension: 向量维度
            encrypted: 是否加密
            auto_flush: 是否自动刷新
            message: 响应消息
            
        Returns:
            Dict: 响应字典
        """
        data = {
            "upsert_count": upsert_count,
            "collection": collection,
            "auto_flush": auto_flush,
            "encrypted": encrypted
        }
        
        if database:
            data["database"] = database
        
        if vector_dimension:
            data["vector_dimension"] = vector_dimension
        
        if auto_generated_vectors > 0 or manual_vectors > 0:
            data["vector_stats"] = {
                "auto_generated_vectors": auto_generated_vectors,
                "manual_vectors": manual_vectors
            }
        
        extra_fields = {}
        if processing_time:
            extra_fields["processing_time"] = processing_time
        if vector_generation_time:
            extra_fields["vector_generation_time"] = vector_generation_time
        
        return ResponseBuilder.success_response(
            data=data,
            message=message,
            extra_fields=extra_fields
        )
    
    @staticmethod
    def delete_response(
        delete_count: int,
        collection: str,
        database: Optional[str] = None,
        delete_method: str = "by_filter",
        processing_time: str = "",
        auto_flush: bool = True,
        message: str = "删除操作成功"
    ) -> Dict[str, Any]:
        """
        构建删除响应
        
        Args:
            delete_count: 删除的记录数
            collection: 集合名称
            database: 数据库名称
            delete_method: 删除方法
            processing_time: 处理时间
            auto_flush: 是否自动刷新
            message: 响应消息
            
        Returns:
            Dict: 响应字典
        """
        data = {
            "delete_count": delete_count,
            "collection": collection,
            "delete_method": delete_method,
            "auto_flush": auto_flush
        }
        
        if database:
            data["database"] = database
        
        extra_fields = {}
        if processing_time:
            extra_fields["processing_time"] = processing_time
        
        return ResponseBuilder.success_response(
            data=data,
            message=message,
            extra_fields=extra_fields
        )


class ErrorResponseBuilder:
    """错误响应构建器"""
    
    @staticmethod
    def validation_error_response(error_message: str) -> Dict[str, Any]:
        """构建验证错误响应"""
        return ResponseBuilder.error_response(
            message=f"参数验证失败: {error_message}",
            code=400,
            error_code="VALIDATION_ERROR"
        )
    
    @staticmethod
    def database_error_response(error_message: str, database: Optional[str] = None) -> Dict[str, Any]:
        """构建数据库错误响应"""
        message = f"数据库操作失败: {error_message}"
        if database:
            message = f"数据库 '{database}' 操作失败: {error_message}"
        
        return ResponseBuilder.error_response(
            message=message,
            code=500,
            error_code="DATABASE_ERROR"
        )
    
    @staticmethod
    def file_processing_error_response(error_message: str, filename: str = "") -> Dict[str, Any]:
        """构建文件处理错误响应"""
        message = f"文件处理失败: {error_message}"
        if filename:
            message = f"文件 '{filename}' 处理失败: {error_message}"
        
        return ResponseBuilder.error_response(
            message=message,
            code=400,
            error_code="FILE_PROCESSING_ERROR"
        )
    
    @staticmethod
    def vector_operation_error_response(error_message: str, operation: str = "向量操作") -> Dict[str, Any]:
        """构建向量操作错误响应"""
        return ResponseBuilder.error_response(
            message=f"{operation}失败: {error_message}",
            code=500,
            error_code="VECTOR_OPERATION_ERROR"
        )
    
    @staticmethod
    def embedding_error_response(error_message: str) -> Dict[str, Any]:
        """构建向量生成错误响应"""
        return ResponseBuilder.error_response(
            message=f"向量生成失败: {error_message}",
            code=500,
            error_code="EMBEDDING_ERROR"
        )
    
    @staticmethod
    def encryption_error_response(error_message: str) -> Dict[str, Any]:
        """构建加密错误响应"""
        return ResponseBuilder.error_response(
            message=f"加密操作失败: {error_message}",
            code=500,
            error_code="ENCRYPTION_ERROR"
        )
    
    @staticmethod
    def collection_not_found_response(collection: str, database: Optional[str] = None) -> Dict[str, Any]:
        """构建集合不存在响应"""
        message = f"集合 '{collection}' 不存在"
        if database:
            message = f"数据库 '{database}' 中的集合 '{collection}' 不存在"
        
        return ResponseBuilder.error_response(
            message=message,
            code=404,
            error_code="COLLECTION_NOT_FOUND"
        )
    
    @staticmethod
    def database_not_found_response(database: str) -> Dict[str, Any]:
        """构建数据库不存在响应"""
        return ResponseBuilder.error_response(
            message=f"数据库 '{database}' 不存在",
            code=404,
            error_code="DATABASE_NOT_FOUND"
        )


def handle_exception_to_response(e: Exception, operation: str = "操作") -> Dict[str, Any]:
    """
    将异常转换为响应
    
    Args:
        e: 异常对象
        operation: 操作名称
        
    Returns:
        Dict: 错误响应
    """
    error_message = str(e)
    
    # 根据异常类型返回不同的错误响应
    if "validation" in error_message.lower():
        return ErrorResponseBuilder.validation_error_response(error_message)
    elif "database" in error_message.lower() or "connection" in error_message.lower():
        return ErrorResponseBuilder.database_error_response(error_message)
    elif "file" in error_message.lower() or "document" in error_message.lower():
        return ErrorResponseBuilder.file_processing_error_response(error_message)
    elif "vector" in error_message.lower() or "embedding" in error_message.lower():
        return ErrorResponseBuilder.vector_operation_error_response(error_message, operation)
    elif "encrypt" in error_message.lower() or "decrypt" in error_message.lower():
        return ErrorResponseBuilder.encryption_error_response(error_message)
    elif "collection not found" in error_message.lower():
        # 尝试从错误消息中提取集合名称
        import re
        match = re.search(r"collection '([^']+)'", error_message)
        collection = match.group(1) if match else "unknown"
        return ErrorResponseBuilder.collection_not_found_response(collection)
    elif "database not found" in error_message.lower():
        # 尝试从错误消息中提取数据库名称
        import re
        match = re.search(r"database '([^']+)'", error_message)
        database = match.group(1) if match else "unknown"
        return ErrorResponseBuilder.database_not_found_response(database)
    else:
        # 通用错误
        return ResponseBuilder.error_response(
            message=f"{operation}失败: {error_message}",
            code=500,
            error_code="INTERNAL_ERROR"
        ) 