#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus数据迁移配置文件

使用说明：
1. 源数据库配置：如果留空，将自动从环境变量读取
2. 目标数据库配置：必须填写，这是数据迁移的目标位置
3. 修改配置后保存文件，然后运行迁移脚本
"""

# 源数据库配置（当前使用的数据库）
# 如果留空，将从环境变量 VECTOR_DB_URI, VECTOR_DB_TOKEN, VECTOR_DB_DATABASE 读取
SOURCE_CONFIG = {
    "uri": "",  # 可选：源Milvus的URI，留空则从环境变量读取
    "token": "",  # 可选：源Milvus的Token，留空则从环境变量读取
    "database": "",  # 可选：源数据库名称，留空则从环境变量读取
}

# 目标数据库配置（新的Milvus数据库）
# ⚠️ 必须填写：这是数据迁移的目标位置
TARGET_CONFIG = {
    "uri": "",  # 必填：目标Milvus的URI，例如: "https://your-target-milvus.com:19530"
    "token": "",  # 可选：目标Milvus的Token（如果需要认证）
    "database": "",  # 可选：目标数据库名称（留空使用默认数据库）
}

# 迁移配置
MIGRATION_CONFIG = {
    "batch_size": 1000,  # 每批迁移的记录数
    "verify_after_migration": True,  # 迁移后是否验证数据
    "skip_existing_collections": True,  # 是否跳过已存在的集合
    "preserve_collection_names": True,  # 是否保持原集合名称
}

# 日志配置
LOGGING_CONFIG = {
    "log_level": "INFO",
    "log_file": "migration.log",
    "console_output": True,
}

# 示例目标数据库配置（请根据实际情况修改）
EXAMPLE_CONFIGS = {
    "local_milvus": {
        "uri": "http://localhost:19530",
        "token": "",
        "database": "default"
    },
    "cloud_milvus": {
        "uri": "https://your-cloud-milvus.com:19530",
        "token": "your-cloud-token",
        "database": "production"
    },
    "zilliz_cloud": {
        "uri": "https://your-cluster.zillizcloud.com:19530",
        "token": "your-zilliz-token",
        "database": "default"
    }
}

# 配置验证函数
def validate_config():
    """验证配置是否正确"""
    errors = []
    warnings = []

    # 检查目标配置
    if not TARGET_CONFIG.get("uri"):
        errors.append("TARGET_CONFIG 中的 uri 不能为空")

    # 检查源配置
    import os
    source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
    if not source_uri:
        warnings.append("源数据库URI未配置，请确保环境变量 VECTOR_DB_URI 已设置")

    # 检查批次大小
    batch_size = MIGRATION_CONFIG.get("batch_size", 0)
    if not isinstance(batch_size, int) or batch_size <= 0:
        errors.append("MIGRATION_CONFIG 中的 batch_size 必须是正整数")

    # 输出结果
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")

    if warnings:
        print("⚠️ 配置警告:")
        for warning in warnings:
            print(f"  - {warning}")

    if not errors and not warnings:
        print("✅ 配置验证通过")

    return len(errors) == 0

def show_current_config():
    """显示当前配置"""
    import os

    print("=== 当前配置 ===")

    # 源数据库配置
    source_uri = SOURCE_CONFIG.get("uri") or os.getenv("VECTOR_DB_URI", "")
    source_db = SOURCE_CONFIG.get("database") or os.getenv("VECTOR_DB_DATABASE", "")
    print(f"源数据库:")
    print(f"  URI: {source_uri or '未配置'}")
    print(f"  Database: {source_db or '默认'}")

    # 目标数据库配置
    print(f"目标数据库:")
    print(f"  URI: {TARGET_CONFIG.get('uri') or '未配置'}")
    print(f"  Database: {TARGET_CONFIG.get('database') or '默认'}")

    # 迁移配置
    print(f"迁移设置:")
    print(f"  批次大小: {MIGRATION_CONFIG.get('batch_size')}")
    print(f"  验证迁移: {MIGRATION_CONFIG.get('verify_after_migration')}")
    print(f"  跳过已存在: {MIGRATION_CONFIG.get('skip_existing_collections')}")

if __name__ == "__main__":
    print("🔧 Milvus迁移配置验证工具")
    print("=" * 50)

    show_current_config()
    print()
    validate_config()
