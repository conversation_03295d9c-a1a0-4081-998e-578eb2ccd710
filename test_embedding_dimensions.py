import requests
import json

def test_embedding_dimensions():
    """测试不同embedding类型的向量维度"""
    
    print("🔍 测试不同embedding类型的向量维度")
    print("=" * 80)
    
    # 测试文本
    test_text = "测试向量维度"
    
    # 测试不同的embedding类型
    embedding_types = [
        "azure-openai",
        "huggingface", 
        "bge-m3",
        None  # 默认类型
    ]
    
    url = "http://copilot.csvw.com/rag_service/api/v1/embeddings"
    
    for embedding_type in embedding_types:
        embedding_name = embedding_type or "default"
        print(f"\n📋 测试 {embedding_name} embedding")
        print("-" * 60)
        
        payload = {
            "text": test_text
        }
        
        if embedding_type:
            payload["embedding_type"] = embedding_type
        
        try:
            response = requests.post(url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and 'vector' in result['data']:
                    vector = result['data']['vector']
                    dimension = len(vector)
                    byte_size = dimension * 4  # float32 = 4 bytes
                    
                    print(f"✅ {embedding_name}:")
                    print(f"   向量维度: {dimension}")
                    print(f"   字节大小: {byte_size}")
                    
                    # 检查是否匹配集合期望的维度
                    if byte_size == 12288:  # 3072维 * 4字节
                        print(f"   🎯 匹配！这个embedding类型与doc集合兼容")
                    elif byte_size == 7168:  # 1792维 * 4字节  
                        print(f"   ⚠️  这个是当前查询使用的维度（不匹配）")
                    else:
                        print(f"   ❌ 不匹配集合期望的12288字节")
                else:
                    print(f"❌ {embedding_name}: 响应格式异常")
                    print(f"   响应: {result}")
            else:
                print(f"❌ {embedding_name}: 请求失败 (状态码: {response.status_code})")
                print(f"   错误: {response.text}")
                
        except Exception as e:
            print(f"❌ {embedding_name}: 请求异常 - {str(e)}")

def test_correct_embedding():
    """使用正确的embedding类型测试搜索"""
    
    print("\n🔧 使用正确的embedding类型测试搜索")
    print("=" * 80)
    
    # 根据维度分析，doc集合期望3072维向量
    # 这通常对应Azure OpenAI的text-embedding-3-large模型
    
    payload = {
        "text": "研发D-FMEA系统操作手册",
        "top_k": 5,
        "total_results": 10,
        "databases": [
            {
                "database": "CSKB_EmpZero",
                "collections": ["doc"]
            }
        ],
        "embedding_type": "azure-openai",  # 确保使用Azure OpenAI
        "search_strategy": "semantic",
        "rerank_strategy": "rrf",
        "rrf_k": 50,
    }
    
    url = "http://copilot.csvw.com/rag_service/api/v1/multi_search_hybrid"
    
    try:
        print("🧪 测试搜索（使用Azure OpenAI embedding）")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=payload)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            results = data.get('results', [])
            
            print(f"✅ 搜索成功！返回 {len(results)} 条结果")
            
            if results:
                print("\n前3条结果:")
                for i, r in enumerate(results[:3]):
                    print(f"  {i+1}. {r.get('content', '')[:100]}...")
                    print(f"     相似度: {r.get('distance', 0):.4f}")
                    print(f"     集合: {r.get('collection', 'unknown')}")
            else:
                print("⚠️  搜索成功但没有返回结果，可能是相似度太低")
                
        else:
            print("❌ 搜索失败")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 搜索异常: {str(e)}")

def check_azure_openai_models():
    """检查Azure OpenAI的不同模型维度"""
    
    print("\n📊 Azure OpenAI模型维度对照")
    print("=" * 80)
    
    models_info = {
        "text-embedding-3-small": {"dimension": 1536, "bytes": 1536 * 4},
        "text-embedding-3-large": {"dimension": 3072, "bytes": 3072 * 4},
        "text-embedding-ada-002": {"dimension": 1536, "bytes": 1536 * 4}
    }
    
    print("Azure OpenAI模型维度信息:")
    for model, info in models_info.items():
        status = "🎯 匹配!" if info["bytes"] == 12288 else "❌ 不匹配"
        print(f"  {model}:")
        print(f"    维度: {info['dimension']}")
        print(f"    字节: {info['bytes']}")
        print(f"    状态: {status}")
    
    print(f"\ndoc集合期望: 12288字节 (3072维)")
    print(f"当前查询生成: 7168字节 (1792维)")
    print(f"\n💡 结论: doc集合很可能是用text-embedding-3-large模型创建的")

if __name__ == "__main__":
    test_embedding_dimensions()
    check_azure_openai_models()
    test_correct_embedding()
