import requests
import json
import sys
import time

# 测试多数据库搜索API
def test_multi_search_api():
    # 设置API地址 - 根据实际部署环境修改
    url = "http://copilot.csvw.com/rag_service/api/v1/multi_search"
    
    # # 测试用例1: 使用字符串指定数据库（搜索所有集合）
    # payload1 = {
    #     "text": "这是第五条测试文本，包含一些不同的内容。",
    #     "top_k": 5,
    #     "total_results": 10,
    #     "databases": ["rag_test_lkz","rag_test_lkz_V1"]
    # }
    
    # 测试用例2: 使用对象指定数据库和集合
    payload2 = {
        "text": "ID.6 X的车机系统与ID.4 X一样么",
        "top_k": 5,
        "total_results": 3,
        "databases": [
            {
                "database": "SVWServiceTest",
                "collections": ["FaqQuestionIndex"]
            },
            {
                "database": "SVWServiceTest",
                "collections": ["FaqAnswerIndex"]
            }
        ]
    }
    
    # # 测试用例3: 混合使用字符串和对象
    # payload3 = {
    #     "text": "这是一个测试查询",
    #     "top_k": 5,
    #     "total_results": 10,
    #     "databases": [
    #         "rag_test_lkz",
    #         {
    #             "database": "rag_test_lkz",
    #             "collections": ["test_texts"]
    #         }
    #     ]
    # }
    
    # 测试所有用例
    test_cases = [
        # ("使用字符串指定数据库", payload1)
        ("使用对象指定数据库和集合", payload2)
        # ("混合使用字符串和对象", payload3)
    ]
    
    for name, payload in test_cases:
        print(f"\n\n测试用例: {name}")
        print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            headers = {"Content-Type": "application/json"}
            
            # 转换为JSON字符串
            payload_str = json.dumps(payload)
                
            print(f"发送请求到: {url}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 发送请求
            response = requests.post(url, data=payload_str, headers=headers)
            
            # 计算耗时
            elapsed_time = time.time() - start_time
            
            print(f"状态码: {response.status_code}")
            print(f"请求耗时: {elapsed_time:.3f}秒")
            
            if response.status_code == 200:
                # 解析JSON响应
                try:
                    result = response.json()
                    print(f"查询文本: {result.get('query')}")
                    print(f"搜索耗时: {result.get('search_time')}")
                    print(f"搜索的数据库数量: {result.get('databases_searched')}")
                    print(f"总结果数: {result.get('total_results')}")
                    
                    # 打印前3条结果
                    results = result.get('results', [])
                    print(f"\n找到 {len(results)} 条结果:")
                    for i, r in enumerate(results):
                        print(f"  结果 {i+1}:")
                        print(f"    内容: {r.get('content', '')[:100]}...")
                        print(f"    相似度: {r.get('similarity', 0):.4f}")
                        print(f"    数据库: {r.get('database', '')}")
                        print(f"    集合: {r.get('collection', '')}")
                    
                    if len(results) > 3:
                        print(f"  ... 还有 {len(results) - 3} 条结果")
                    
                    print("✅ 测试成功!")
                except json.JSONDecodeError:
                    print("❌ 响应不是有效的JSON格式!")
                    print(f"响应体: {response.text}")
            else:
                print("❌ 测试失败!")
                print(f"响应体: {response.text}")
        except Exception as e:
            print(f"❌ 测试出错: {e}")

if __name__ == "__main__":
    test_multi_search_api()
