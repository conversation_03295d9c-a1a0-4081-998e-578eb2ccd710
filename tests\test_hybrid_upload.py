"""
混合检索批量上传接口调用示例
测试 /upload_texts_batch_hybrid 接口
"""

import requests
import json
import time

# 配置API基础URL
API_BASE_URL = "http://copilot.csvw.com"  
UPLOAD_ENDPOINT = f"{API_BASE_URL}/rag_service/api/v1/upload_texts_batch_hybrid"

def test_hybrid_upload_basic():
    """基础混合检索上传测试"""
    print("=" * 60)
    print("📝 基础混合检索上传测试")
    print("=" * 60)
    
    # 准备测试数据
    request_data = {
        "items": [
            {
                "text": "苹果iPhone 15 Pro，配备A17 Pro芯片，拍照效果出色",
                "metadata": {
                    "category": "电子产品",
                    "brand": "苹果",
                    "price": 7999,
                    "in_stock": True
                }
            },
            {
                "text": "小米13 Ultra，徕卡影像系统，性价比很高的拍照手机",
                "metadata": {
                    "category": "电子产品", 
                    "brand": "小米",
                    "price": 5999,
                    "in_stock": True
                }
            },
            {
                "text": "华为Mate 60 Pro，麒麟9000S芯片，支持卫星通话",
                "metadata": {
                    "category": "电子产品",
                    "brand": "华为", 
                    "price": 6999,
                    "in_stock": False
                }
            }
        ],
        "collection": "products_hybrid",
        "database": "lkz_test_V1",
        "global_metadata": {
            "upload_time": int(time.time()),
            "source": "product_catalog"
        },
        "encrypt": False,
        "batch_size": 10
    }
    
    # 发送请求
    try:
        print(f"🚀 发送请求到: {UPLOAD_ENDPOINT}")
        print(f"📊 数据项数量: {len(request_data['items'])}")
        print(f"📁 集合名称: {request_data['collection']}")
        print(f"🗄️ 数据库: {request_data['database']}")
        
        start_time = time.time()
        response = requests.post(
            UPLOAD_ENDPOINT,
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=60  # 60秒超时
        )
        end_time = time.time()
        
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f}秒")
        print(f"📡 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 上传成功!")
            print(f"📄 响应结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print("❌ 上传失败!")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_hybrid_upload_with_custom_ids():
    """使用自定义ID的混合检索上传测试"""
    print("\n" + "=" * 60)
    print("🆔 自定义ID混合检索上传测试")
    print("=" * 60)
    
    request_data = {
        "items": [
            {
                "text": "白色AirPods Pro，主动降噪，无线充电",
                "metadata": {
                    "category": "音频设备",
                    "color": "白色",
                    "wireless": True
                },
                "id": "airpods_pro_001"  # 自定义字符串ID
            },
            {
                "text": "索尼WH-1000XM4头戴式耳机，降噪效果一流",
                "metadata": {
                    "category": "音频设备",
                    "color": "黑色", 
                    "wireless": True
                },
                "id": "sony_wh1000xm4_001"  # 自定义字符串ID
            }
        ],
        "collection": "audio_products_hybrid",
        "database": "lkz_test_V1",
        "encrypt": False,
        "embedding_type": "huggingface",  # 指定向量模型
        "batch_size": 5
    }
    
    try:
        print(f"🚀 发送自定义ID请求...")
        response = requests.post(UPLOAD_ENDPOINT, json=request_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 自定义ID上传成功!")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 上传失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def test_hybrid_upload_with_encryption():
    """加密数据的混合检索上传测试"""
    print("\n" + "=" * 60)
    print("🔐 加密数据混合检索上传测试")
    print("=" * 60)
    
    request_data = {
        "items": [
            {
                "text": "机密产品规格文档，包含核心技术参数",
                "metadata": {
                    "confidential_level": "high",
                    "department": "研发部"
                }
            },
            {
                "text": "客户敏感信息，联系方式和购买记录",
                "metadata": {
                    "confidential_level": "medium",
                    "department": "销售部"
                }
            }
        ],
        "collection": "confidential_docs_hybrid",
        "database": "lkz_test_V1",
        "encrypt": True,  # 启用加密
        "global_metadata": {
            "security_level": "encrypted",
            "access_control": "admin_only"
        }
    }
    
    try:
        print(f"🔐 发送加密数据请求...")
        response = requests.post(UPLOAD_ENDPOINT, json=request_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 加密数据上传成功!")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 上传失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def test_hybrid_upload_with_dynamic_fields():
    """包含动态字段的混合检索上传测试"""
    print("\n" + "=" * 60)
    print("🔧 动态字段混合检索上传测试")
    print("=" * 60)
    
    request_data = {
        "items": [
            {
                "text": "2024年春季新款连衣裙，时尚舒适",
                "metadata": {
                    "season": "spring",
                    "year": 2024
                },
                # 动态字段（会自动添加为独立列）
                "product_code": "DRESS_SP24_001",
                "size_available": ["S", "M", "L", "XL"],
                "material": "棉麻混纺",
                "rating": 4.8
            },
            {
                "text": "商务男士西装套装，正式场合首选",
                "metadata": {
                    "occasion": "business",
                    "gender": "male"
                },
                # 更多动态字段
                "product_code": "SUIT_BUS_001", 
                "size_available": ["48", "50", "52", "54"],
                "material": "羊毛",
                "rating": 4.6,
                "brand_tier": "premium"
            }
        ],
        "collection": "fashion_hybrid",
        "database": "lkz_test_V1",
        "global_metadata": {
            "catalog_version": "2024.1",
            "upload_batch": "fashion_spring_2024"
        }
    }
    
    try:
        print(f"🔧 发送包含动态字段的请求...")
        response = requests.post(UPLOAD_ENDPOINT, json=request_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 动态字段数据上传成功!")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 上传失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def test_large_batch_upload():
    """大批量数据混合检索上传测试"""
    print("\n" + "=" * 60)
    print("📦 大批量混合检索上传测试")
    print("=" * 60)
    
    # 生成大批量测试数据
    items = []
    for i in range(50):  # 生成50条测试数据
        items.append({
            "text": f"测试产品 #{i+1}，这是一个用于测试的示例产品描述，包含各种特性和功能介绍。",
            "metadata": {
                "product_id": f"TEST_{i+1:03d}",
                "category": "测试类别",
                "test_batch": True,
                "index": i + 1
            }
        })
    
    request_data = {
        "items": items,
        "collection": "test_large_batch_hybrid_V1",
        "database": "lkz_test_V1",
        "embedding_type": "azure-openai",
        "batch_size": 20,  # 分批处理，每批20条
        "global_metadata": {
            "test_type": "large_batch",
            "total_items": len(items)
        }
    }
    
    try:
        print(f"📦 发送大批量数据请求 ({len(items)} 条记录)...")
        start_time = time.time()
        response = requests.post(UPLOAD_ENDPOINT, json=request_data, timeout=300)  # 5分钟超时
        end_time = time.time()
        
        print(f"⏱️ 大批量上传耗时: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 大批量数据上传成功!")
            print(f"📊 处理统计:")
            print(f"  - 总记录数: {result.get('text_count', 0)}")
            print(f"  - 批次数: {result.get('batch_count', 0)}")
            print(f"  - 处理时间: {result.get('processing_time', '未知')}")
            print(f"  - 平均每条记录耗时: {result.get('avg_time_per_text', '未知')}")
        else:
            print(f"❌ 上传失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def main():
    """主函数 - 运行所有测试"""
    print("🎯 混合检索批量上传接口测试程序")
    print(f"📡 API地址: {API_BASE_URL}")
    print(f"📝 接口端点: {UPLOAD_ENDPOINT}")
    
    try:
        # 基础功能测试
        test_hybrid_upload_basic()
        
        # 自定义ID测试
        # test_hybrid_upload_with_custom_ids()
        
        # 加密数据测试
        # test_hybrid_upload_with_encryption()
        
        # 动态字段测试
        # test_hybrid_upload_with_dynamic_fields()
        
        # 大批量数据测试
        # test_large_batch_upload()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main() 