from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from pathlib import Path
import hashlib
import re
import io
import os
import sys

# 尝试导入PyMuPDF库，如果失败则设置为None
PYMUPDF_AVAILABLE = False
fitz = None
try:
    # 尝试直接导入pymupdf而不是fitz
    import pymupdf
    fitz = pymupdf
    PYMUPDF_AVAILABLE = True
    print("Successfully imported pymupdf")
except ImportError as e:
    print(f"Warning: pymupdf import failed: {e}")
    try:
        # 如果失败，尝试导入fitz
        import fitz
        PYMUPDF_AVAILABLE = True
        print("Successfully imported fitz")
    except ImportError as e:
        print(f"Warning: fitz import failed: {e}")
    except Exception as e:
        print(f"Warning: Error loading fitz: {e}")

# 尝试导入python-docx库
DOCX_AVAILABLE = False
Document = None
try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError as e:
    print(f"Warning: python-docx not available: {e}")

def parse_pdf(file_bytes: bytes) -> str:
    """解析PDF文件内容"""
    if not PYMUPDF_AVAILABLE:
        # 如果PyMuPDF不可用，尝试使用其他方法解析PDF
        try:
            # 尝试使用pdfplumber
            try:
                import pdfplumber
                with pdfplumber.open(io.BytesIO(file_bytes)) as pdf:
                    return '\n'.join(page.extract_text() or '' for page in pdf.pages)
            except ImportError:
                # 如果pdfplumber不可用，尝试使用PyPDF2
                try:
                    import PyPDF2
                    reader = PyPDF2.PdfReader(io.BytesIO(file_bytes))
                    return '\n'.join(page.extract_text() or '' for page in reader.pages)
                except ImportError:
                    # 如果所有库都不可用，抛出异常
                    raise ImportError("无法解析PDF文件。请安装以下库之一: pymupdf, pdfplumber, PyPDF2")
        except Exception as e:
            raise ValueError(f"PDF解析失败: {str(e)}")

    # 使用PyMuPDF解析
    try:
        doc = fitz.open(stream=file_bytes, filetype="pdf")
        text = ""
        for page in doc:
            text += page.get_text()
        return text
    except Exception as e:
        # 如果PyMuPDF失败，尝试其他方法
        try:
            # 尝试使用pdfplumber
            import pdfplumber
            with pdfplumber.open(io.BytesIO(file_bytes)) as pdf:
                return '\n'.join(page.extract_text() or '' for page in pdf.pages)
        except ImportError:
            # 如果pdfplumber不可用，尝试使用PyPDF2
            try:
                import PyPDF2
                reader = PyPDF2.PdfReader(io.BytesIO(file_bytes))
                return '\n'.join(page.extract_text() or '' for page in reader.pages)
            except ImportError:
                # 如果所有库都不可用，抛出原始异常
                raise ValueError(f"PDF解析失败: {str(e)}")

def parse_txt(content: bytes) -> str:
    try:
        return content.decode('utf-8')
    except UnicodeDecodeError:
        return content.decode('gbk')

def parse_docx(content: bytes) -> str:
    """解析DOCX文件内容"""
    if not DOCX_AVAILABLE:
        # 如果python-docx不可用，尝试使用其他方法
        try:
            # 尝试使用textract
            try:
                import textract
                return textract.process(content, extension='docx').decode('utf-8')
            except ImportError:
                # 如果textract不可用，抛出异常
                raise ImportError("无法解析DOCX文件。请安装python-docx或textract")
        except Exception as e:
            raise ValueError(f"DOCX解析失败: {str(e)}")

    # 使用python-docx解析
    try:
        doc = Document(io.BytesIO(content))
        return '\n'.join([para.text for para in doc.paragraphs if para.text])
    except Exception as e:
        # 如果python-docx失败，尝试其他方法
        try:
            import textract
            return textract.process(content, extension='docx').decode('utf-8')
        except ImportError:
            # 如果textract不可用，抛出原始异常
            raise ValueError(f"DOCX解析失败: {str(e)}")

class DocumentChunk:
    def __init__(
        self,
        document_id: str,
        content: str,
        metadata: Dict[str, Any],
        image_paths: Optional[List[str]] = None
    ):
        self.document_id = document_id
        self.content = content
        self.metadata = metadata
        self.image_paths = image_paths or []

    def to_vector_record(self, vector: List[float]) -> Dict[str, Any]:
        return {
            "vector": vector,
            "attributes": {
                "doc_id": self.document_id,
                "content": self.content,
                **self.metadata,
                "images": self.image_paths
            }
        }

class DocumentParser(ABC):
    @abstractmethod
    def parse(self, file_path: str, images_dir: str = "data/images") -> List[DocumentChunk]:
        pass

class PDFParser(DocumentParser):
    def __init__(self, extract_images: bool = True):
        self.extract_images = extract_images

    def parse(self, file_path: str, images_dir: str = "data/images") -> List[DocumentChunk]:
        doc_id = hashlib.md5(file_path.encode()).hexdigest()
        chunks = []
        Path(images_dir).mkdir(parents=True, exist_ok=True)

        # 检查PyMuPDF是否可用
        if not PYMUPDF_AVAILABLE:
            # 如果PyMuPDF不可用，尝试其他方法
            try:
                # 尝试使用pdfplumber
                try:
                    import pdfplumber
                    with pdfplumber.open(file_path) as pdf:
                        full_text = '\n'.join(page.extract_text() or '' for page in pdf.pages)
                        chunk = DocumentChunk(
                            document_id=doc_id,
                            content=full_text.strip(),
                            metadata={"source": file_path, "pages": len(pdf.pages)},
                            image_paths=[]
                        )
                        chunks.append(chunk)
                        return chunks
                except ImportError:
                    # 如果pdfplumber不可用，尝试使用PyPDF2
                    try:
                        import PyPDF2
                        with open(file_path, 'rb') as f:
                            reader = PyPDF2.PdfReader(f)
                            full_text = '\n'.join(page.extract_text() or '' for page in reader.pages)
                            chunk = DocumentChunk(
                                document_id=doc_id,
                                content=full_text.strip(),
                                metadata={"source": file_path, "pages": len(reader.pages)},
                                image_paths=[]
                            )
                            chunks.append(chunk)
                            return chunks
                    except ImportError:
                        # 如果所有库都不可用，抛出异常
                        raise ImportError("无法解析PDF文件。请安装以下库之一: pymupdf, pdfplumber, PyPDF2")
            except Exception as e:
                raise ValueError(f"PDF解析失败: {str(e)}")

        # 使用PyMuPDF解析
        try:
            import fitz  # PyMuPDF
            with fitz.open(file_path) as doc:
                full_text = ""
                images = []

                for page in doc:
                    full_text += page.get_text() + "\n"

                    if self.extract_images:
                        for img_index, img in enumerate(page.get_images()):
                            base_image = doc.extract_image(img[0])
                            image_path = f"{images_dir}/{doc_id}_p{page.number}_i{img_index}.{base_image['ext']}"
                            with open(image_path, "wb") as f:
                                f.write(base_image["image"])
                            images.append(image_path)

                chunk = DocumentChunk(
                    document_id=doc_id,
                    content=full_text.strip(),
                    metadata={"source": file_path, "pages": len(doc)},
                    image_paths=images
                )
                chunks.append(chunk)
            return chunks
        except Exception as e:
            # 如果PyMuPDF失败，尝试其他方法
            try:
                # 尝试使用pdfplumber
                import pdfplumber
                with pdfplumber.open(file_path) as pdf:
                    full_text = '\n'.join(page.extract_text() or '' for page in pdf.pages)
                    chunk = DocumentChunk(
                        document_id=doc_id,
                        content=full_text.strip(),
                        metadata={"source": file_path, "pages": len(pdf.pages)},
                        image_paths=[]
                    )
                    chunks.append(chunk)
                    return chunks
            except ImportError:
                # 如果pdfplumber不可用，尝试使用PyPDF2
                try:
                    import PyPDF2
                    with open(file_path, 'rb') as f:
                        reader = PyPDF2.PdfReader(f)
                        full_text = '\n'.join(page.extract_text() or '' for page in reader.pages)
                        chunk = DocumentChunk(
                            document_id=doc_id,
                            content=full_text.strip(),
                            metadata={"source": file_path, "pages": len(reader.pages)},
                            image_paths=[]
                        )
                        chunks.append(chunk)
                        return chunks
                except ImportError:
                    # 如果所有库都不可用，抛出原始异常
                    raise ValueError(f"PDF解析失败: {str(e)}")

class HTMLParser(DocumentParser):
    def parse(self, file_path: str, images_dir: str = "data/images") -> List[DocumentChunk]:
        from bs4 import BeautifulSoup

        doc_id = hashlib.md5(file_path.encode()).hexdigest()
        with open(file_path, "r", encoding="utf-8") as f:
            soup = BeautifulSoup(f.read(), "lxml")

        # 移除脚本和样式
        for script in soup(["script", "style"]):
            script.decompose()

        text = soup.get_text(separator="\n", strip=True)
        images = [img["src"] for img in soup.find_all("img") if img.get("src")]

        return [DocumentChunk(
            document_id=doc_id,
            content=text,
            metadata={"source": file_path},
            image_paths=images
        )]

class ChunkStrategy(ABC):
    @abstractmethod
    def chunk(self, content: str, metadata: Dict) -> List[DocumentChunk]:
        pass

class WholeDocumentStrategy(ChunkStrategy):
    def chunk(self, content: str, metadata: Dict) -> List[DocumentChunk]:
        return [DocumentChunk(
            document_id=metadata["doc_id"],
            content=content,
            metadata=metadata
        )]

class FixedSizeChunkStrategy(ChunkStrategy):
    def __init__(self, chunk_size: int = 1000, overlap: int = 100):
        self.chunk_size = chunk_size
        self.overlap = overlap

    def chunk(self, content: str, metadata: Dict) -> List[DocumentChunk]:
        chunks = []
        start = 0
        while start < len(content):
            end = start + self.chunk_size
            chunks.append(DocumentChunk(
                document_id=metadata["doc_id"],
                content=content[start:end],
                metadata={"chunk_index": len(chunks), **metadata}
            ))
            start = end - self.overlap
        return chunks