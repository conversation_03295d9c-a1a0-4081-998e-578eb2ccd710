"""
混合检索操作Repository

负责：
- 混合检索（语义检索 + 全文检索）
- 密集向量和稀疏向量搜索
- 重排序策略（RRF、加权融合）
- 搜索策略选择
"""

from typing import List, Dict, Any, Optional
from app.core.vectordb import VectorDatabase


class HybridRepository:
    """混合检索操作Repository"""
    
    def __init__(self, db: VectorDatabase):
        """
        初始化混合检索操作Repository
        
        Args:
            db: 数据库连接实例
        """
        self.db = db
    
    async def search_hybrid_vectors(
        self,
        collection_name: str,
        query_text: str,
        dense_vector: List[float],
        top_k: int = 10,
        filter_expr: str = "",
        search_strategy: str = "hybrid",
        rerank_strategy: str = "rrf",
        rrf_k: int = 60,
        dense_weight: float = 0.6,
        sparse_weight: float = 0.4
    ) -> List[Dict[str, Any]]:
        """
        混合检索搜索
        
        Args:
            collection_name: 集合名称
            query_text: 查询文本
            dense_vector: 密集向量
            top_k: 返回结果数量
            filter_expr: 筛选表达式
            search_strategy: 搜索策略 ('semantic', 'full_text', 'hybrid')
            rerank_strategy: 重排序策略 ('rrf', 'weighted')
            rrf_k: RRF平滑参数
            dense_weight: 语义检索权重
            sparse_weight: 全文检索权重
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
            
        Raises:
            Exception: 搜索失败
        """
        print(f"[混合检索] 集合: '{collection_name}', 策略: {search_strategy}, top_k: {top_k}")
        
        try:
            if hasattr(self.db, 'search_hybrid_vectors'):
                search_method = getattr(self.db, 'search_hybrid_vectors')
                results = await search_method(
                    collection=collection_name,
                    query_text=query_text,
                    dense_vector=dense_vector,
                    top_k=top_k,
                    filter_expr=filter_expr,
                    search_strategy=search_strategy,
                    rerank_strategy=rerank_strategy,
                    rrf_k=rrf_k,
                    dense_weight=dense_weight,
                    sparse_weight=sparse_weight
                )
                
                print(f"[混合检索] 搜索完成，返回 {len(results)} 条结果")
                return results
            else:
                raise Exception("数据库不支持混合检索操作")
        except Exception as e:
            print(f"[错误] 混合检索失败: {e}")
            raise Exception(f"混合检索失败: {str(e)}")
    
    async def search_semantic_only(
        self,
        collection_name: str,
        dense_vector: List[float],
        top_k: int = 10,
        filter_expr: str = ""
    ) -> List[Dict[str, Any]]:
        """
        仅语义检索
        
        Args:
            collection_name: 集合名称
            dense_vector: 密集向量
            top_k: 返回结果数量
            filter_expr: 筛选表达式
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        return await self.search_hybrid_vectors(
            collection_name=collection_name,
            query_text="",
            dense_vector=dense_vector,
            top_k=top_k,
            filter_expr=filter_expr,
            search_strategy="semantic"
        )
    
    async def search_full_text_only(
        self,
        collection_name: str,
        query_text: str,
        top_k: int = 10,
        filter_expr: str = ""
    ) -> List[Dict[str, Any]]:
        """
        仅全文检索
        
        Args:
            collection_name: 集合名称
            query_text: 查询文本
            top_k: 返回结果数量
            filter_expr: 筛选表达式
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        return await self.search_hybrid_vectors(
            collection_name=collection_name,
            query_text=query_text,
            dense_vector=[],  # 空向量
            top_k=top_k,
            filter_expr=filter_expr,
            search_strategy="full_text"
        ) 