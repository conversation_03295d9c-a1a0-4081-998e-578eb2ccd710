"""
数据访问层 (Repositories)

提供数据库访问的抽象层，封装所有与向量数据库的交互逻辑。

主要组件：
- VectorDatabaseRepository: 数据库连接和管理
- CollectionRepository: 集合管理  
- VectorRepository: 向量操作（增删改查）
- SearchRepository: 搜索操作
- HybridRepository: 混合检索操作
"""

from .database_repository import VectorDatabaseRepository
from .collection_repository import CollectionRepository
from .vector_repository import VectorRepository
from .search_repository import SearchRepository
from .hybrid_repository import HybridRepository

__all__ = [
    "VectorDatabaseRepository",
    "CollectionRepository", 
    "VectorRepository",
    "SearchRepository",
    "HybridRepository"
] 