"""
业务逻辑层 (Services)

封装复杂的业务逻辑，协调多个repository的操作，提供高级API接口。

主要组件：
- DocumentService: 文档处理服务（上传、解析、分块、向量化）
- VectorService: 向量操作服务（增删改查、批量操作）
- SearchService: 搜索服务（单库搜索、多库搜索、高级搜索）
- HybridSearchService: 混合检索服务（语义+全文检索）
- DatabaseService: 数据库管理服务（数据库和集合管理）
"""

# 服务层组件
# 注意：某些服务可能需要额外的依赖才能正常工作

try:
    from .database_service import DatabaseService
except ImportError:
    DatabaseService = None

try:
    from .document_service import DocumentService
except ImportError:
    DocumentService = None

try:
    from .vector_service import VectorService
except ImportError:
    VectorService = None

try:
    from .search_service import SearchService
except ImportError:
    SearchService = None

try:
    from .hybrid_search_service import HybridSearchService
except ImportError:
    HybridSearchService = None

try:
    from .embedding_service import EmbeddingService
except ImportError:
    EmbeddingService = None

__all__ = [
    "DatabaseService",
    "DocumentService", 
    "VectorService",
    "SearchService",
    "HybridSearchService",
    "EmbeddingService"
] 