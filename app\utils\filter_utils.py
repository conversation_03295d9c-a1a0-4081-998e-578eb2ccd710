"""
过滤工具模块
提供统一的过滤表达式构建功能
"""
from typing import Dict, Any, List, Union, Optional
from pydantic import BaseModel


class FilterExpressionBuilder:
    """过滤表达式构建器"""
    
    @staticmethod
    def build_metadata_filter_expr(metadata_filters: Dict[str, Any]) -> str:
        """
        构建metadata字段的筛选表达式

        Args:
            metadata_filters: metadata筛选条件字典
            例如: {"category": "electronics", "price": {"$lt": 100}, "in_stock": True}

        Returns:
            str: Milvus筛选表达式
            例如: "metadata[\"category\"] == \"electronics\" and metadata[\"price\"] < 100 and metadata[\"in_stock\"] == true"
        """
        if not metadata_filters:
            return ""

        conditions = []

        for key, value in metadata_filters.items():
            if isinstance(value, dict):
                # 处理操作符，如 {"$lt": 100, "$gt": 50}
                for op, op_value in value.items():
                    condition = FilterExpressionBuilder._build_operator_condition(key, op, op_value)
                    if condition:
                        conditions.append(condition)
            else:
                # 直接值比较
                condition = FilterExpressionBuilder._build_direct_condition(key, value)
                if condition:
                    conditions.append(condition)

        return " and ".join(conditions)
    
    @staticmethod
    def _build_operator_condition(key: str, operator: str, value: Any) -> str:
        """
        构建操作符条件
        
        Args:
            key: 字段名
            operator: 操作符
            value: 值
            
        Returns:
            str: 条件表达式
        """
        if operator == "$lt":
            return f"metadata[\"{key}\"] < {value}"
        elif operator == "$lte":
            return f"metadata[\"{key}\"] <= {value}"
        elif operator == "$gt":
            return f"metadata[\"{key}\"] > {value}"
        elif operator == "$gte":
            return f"metadata[\"{key}\"] >= {value}"
        elif operator == "$eq":
            if isinstance(value, str):
                return f"metadata[\"{key}\"] == \"{value}\""
            else:
                return f"metadata[\"{key}\"] == {str(value).lower()}"
        elif operator == "$ne":
            if isinstance(value, str):
                return f"metadata[\"{key}\"] != \"{value}\""
            else:
                return f"metadata[\"{key}\"] != {str(value).lower()}"
        elif operator == "$in":
            # 处理 in 操作符
            if isinstance(value, list):
                in_values = []
                for v in value:
                    if isinstance(v, str):
                        in_values.append(f"\"{v}\"")
                    else:
                        in_values.append(str(v).lower())
                return f"metadata[\"{key}\"] in [{', '.join(in_values)}]"
        elif operator == "$nin":
            # 处理 not in 操作符
            if isinstance(value, list):
                nin_values = []
                for v in value:
                    if isinstance(v, str):
                        nin_values.append(f"\"{v}\"")
                    else:
                        nin_values.append(str(v).lower())
                return f"metadata[\"{key}\"] not in [{', '.join(nin_values)}]"
        
        return ""
    
    @staticmethod
    def _build_direct_condition(key: str, value: Any) -> str:
        """
        构建直接值条件
        
        Args:
            key: 字段名
            value: 值
            
        Returns:
            str: 条件表达式
        """
        if isinstance(value, str):
            return f"metadata[\"{key}\"] == \"{value}\""
        elif isinstance(value, bool):
            return f"metadata[\"{key}\"] == {str(value).lower()}"
        elif isinstance(value, (int, float)):
            return f"metadata[\"{key}\"] == {value}"
        else:
            # 其他类型转为字符串
            return f"metadata[\"{key}\"] == \"{str(value)}\""
    
    @staticmethod
    def build_direct_field_filter_expr(field_filters: Dict[str, Any]) -> str:
        """
        构建直接字段的筛选表达式（非metadata字段）
        
        Args:
            field_filters: 直接字段筛选条件字典
            例如: {"car_type": "passenger", "model_year": 2020}
            
        Returns:
            str: Milvus筛选表达式
            例如: "car_type == 'passenger' and model_year == 2020"
        """
        if not field_filters:
            return ""
        
        conditions = []
        
        for field, value in field_filters.items():
            if isinstance(value, str):
                conditions.append(f"{field} == '{value}'")
            elif isinstance(value, (int, float, bool)):
                conditions.append(f"{field} == {value}")
            else:
                # 其他类型转为字符串
                conditions.append(f"{field} == '{str(value)}'")
        
        return " and ".join(conditions)
    
    @staticmethod
    def combine_filter_expressions(expressions: List[str]) -> str:
        """
        合并多个筛选表达式
        
        Args:
            expressions: 表达式列表
            
        Returns:
            str: 合并后的表达式
        """
        # 过滤掉空表达式
        valid_expressions = [expr.strip() for expr in expressions if expr and expr.strip()]
        
        if not valid_expressions:
            return ""
        
        return " and ".join(valid_expressions)


class RequestFieldExtractor:
    """请求字段提取器"""
    
    @staticmethod
    def extract_extra_fields(
        request_obj: BaseModel,
        standard_fields: List[str]
    ) -> Dict[str, Any]:
        """
        从请求对象中提取额外字段（动态字段）
        
        Args:
            request_obj: 请求对象
            standard_fields: 标准字段列表
            
        Returns:
            Dict: 额外字段字典
        """
        extra_fields = {}
        
        # 获取所有字段，包括额外字段
        try:
            # 尝试使用新的 model_dump 方法（Pydantic v2）
            all_fields = request_obj.model_dump(exclude_unset=True)
        except AttributeError:
            # 如果失败，回退到旧的 dict 方法（Pydantic v1）
            all_fields = request_obj.dict(exclude_unset=True)
        
        # 遍历所有字段
        for field, value in all_fields.items():
            # 如果不是标准字段且值不为None，则收集为额外字段
            if field not in standard_fields and value is not None:
                extra_fields[field] = value
        
        return extra_fields
    
    @staticmethod
    def extract_filter_conditions_from_request(
        request_obj: BaseModel,
        standard_fields: List[str],
        include_metadata_filters: bool = True,
        include_direct_filter_expr: bool = True,
        include_extra_fields: bool = True
    ) -> List[str]:
        """
        从请求对象中提取所有过滤条件
        
        Args:
            request_obj: 请求对象
            standard_fields: 标准字段列表
            include_metadata_filters: 是否包含metadata筛选
            include_direct_filter_expr: 是否包含直接筛选表达式
            include_extra_fields: 是否包含额外字段筛选
            
        Returns:
            List[str]: 筛选条件列表
        """
        filter_conditions = []
        
        # 1. 如果请求中直接指定了filter_expr，则使用它
        if include_direct_filter_expr:
            filter_expr = getattr(request_obj, 'filter_expr', None)
            if filter_expr:
                filter_conditions.append(filter_expr)
        
        # 2. 如果请求中指定了metadata_filters，则构建metadata筛选表达式
        if include_metadata_filters:
            metadata_filters = getattr(request_obj, 'metadata_filters', None)
            if metadata_filters:
                metadata_filter_expr = FilterExpressionBuilder.build_metadata_filter_expr(metadata_filters)
                if metadata_filter_expr:
                    filter_conditions.append(metadata_filter_expr)
        
        # 3. 检查是否有额外字段可以用于筛选（非metadata字段的直接字段筛选）
        if include_extra_fields and not filter_conditions:  # 只有在没有明确指定筛选条件时才使用额外字段
            extra_fields = RequestFieldExtractor.extract_extra_fields(request_obj, standard_fields)
            if extra_fields:
                extra_filter_expr = FilterExpressionBuilder.build_direct_field_filter_expr(extra_fields)
                if extra_filter_expr:
                    filter_conditions.append(extra_filter_expr)
        
        return filter_conditions


class FilterValidator:
    """过滤条件验证器"""
    
    @staticmethod
    def validate_filter_expression(filter_expr: str) -> bool:
        """
        验证过滤表达式的基本语法
        
        Args:
            filter_expr: 过滤表达式
            
        Returns:
            bool: 是否有效
        """
        if not filter_expr or not filter_expr.strip():
            return True  # 空表达式是有效的
        
        # 基本的语法检查
        try:
            # 检查括号是否匹配
            if filter_expr.count('(') != filter_expr.count(')'):
                return False
            
            # 检查引号是否匹配
            if filter_expr.count('"') % 2 != 0:
                return False
            
            if filter_expr.count("'") % 2 != 0:
                return False
            
            # 检查是否包含基本的操作符
            operators = ['==', '!=', '<', '<=', '>', '>=', 'in', 'not in', 'and', 'or']
            has_operator = any(op in filter_expr for op in operators)
            
            return has_operator
            
        except Exception:
            return False
    
    @staticmethod
    def validate_metadata_filters(metadata_filters: Dict[str, Any]) -> bool:
        """
        验证metadata筛选条件
        
        Args:
            metadata_filters: metadata筛选条件
            
        Returns:
            bool: 是否有效
        """
        if not metadata_filters:
            return True
        
        for key, value in metadata_filters.items():
            # 验证键名
            if not isinstance(key, str) or not key:
                return False
            
            # 验证值
            if isinstance(value, dict):
                # 验证操作符字典
                valid_operators = ['$lt', '$lte', '$gt', '$gte', '$eq', '$ne', '$in', '$nin']
                for op in value.keys():
                    if op not in valid_operators:
                        return False
            elif value is None:
                return False
        
        return True


# 便捷函数
def build_combined_filter_expression(
    request_obj: BaseModel,
    standard_fields: List[str],
    include_metadata_filters: bool = True,
    include_direct_filter_expr: bool = True,
    include_extra_fields: bool = True
) -> str:
    """
    构建组合的过滤表达式
    
    Args:
        request_obj: 请求对象
        standard_fields: 标准字段列表
        include_metadata_filters: 是否包含metadata筛选
        include_direct_filter_expr: 是否包含直接筛选表达式
        include_extra_fields: 是否包含额外字段筛选
        
    Returns:
        str: 组合的过滤表达式
    """
    filter_conditions = RequestFieldExtractor.extract_filter_conditions_from_request(
        request_obj,
        standard_fields,
        include_metadata_filters,
        include_direct_filter_expr,
        include_extra_fields
    )
    
    return FilterExpressionBuilder.combine_filter_expressions(filter_conditions)


def get_search_standard_fields() -> List[str]:
    """获取搜索请求的标准字段列表"""
    return ["text", "top_k", "collection", "database", "embedding_type", "filter_expr", "metadata_filters"]


def get_multi_search_standard_fields() -> List[str]:
    """获取多数据库搜索请求的标准字段列表"""
    return ["text", "top_k", "databases", "total_results", "embedding_type", "filter_expr", "metadata_filters"]


def get_upload_standard_fields() -> List[str]:
    """获取上传请求的标准字段列表"""
    return ["texts", "collection", "database", "metadata", "encrypt", "embedding_type", "ids"] 