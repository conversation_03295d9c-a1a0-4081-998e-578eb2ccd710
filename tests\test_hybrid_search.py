"""
混合检索多数据库搜索接口调用示例
测试 /multi_search_hybrid 接口

⚠️ 测试前提条件：
1. 确保目标数据库已存在（如：documents_hybrid, knowledge_base）
2. 确保集合支持混合检索（使用create_hybrid_collection创建）
3. 确保集合中有测试数据（使用insert_hybrid_vectors插入）

支持的检索策略：
1. 'semantic' - 仅语义检索（使用密集向量）
2. 'full_text' - 仅全文检索（使用稀疏向量/BM25）
3. 'hybrid' - 混合检索（语义+全文）

重排序策略说明：
1. RRF（倒数排名融合）：
   - 参数：rrf_k (平滑参数，范围[10,100]，推荐60)
   - 公式：1/(k+rank)
   - 适用：需要平衡两种检索结果的场景

2. Weighted（加权融合）：
   - 参数：dense_weight, sparse_weight (权重范围[0,1])
   - 适用：需要偏重某种检索类型的场景
   - 例如：更重视语义检索用(0.7, 0.3)，更重视关键词用(0.3, 0.7)

🔧 如果测试失败，请检查：
- 数据库和集合是否存在
- 集合是否支持混合检索功能
- 是否有足够的测试数据
"""

import requests
import json
import time
from typing import Dict, Any

# 配置API基础URL
API_BASE_URL = "http://copilot.csvw.com"  
SEARCH_ENDPOINT = f"{API_BASE_URL}/rag_service/api/v1/multi_search_hybrid"

def test_hybrid_search_semantic_only():
    """测试仅语义检索模式"""
    print("=" * 60)
    print("🔍 测试仅语义检索模式")
    print("=" * 60)
    
    # 准备测试数据
    request_data = {
        "text": "iPhone",
        "top_k": 5,
        "total_results": 10,
        "databases": [
            {
                "database": "lkz_test_V1",
                "collections": ["products_hybrid"]  # 正确指定集合
                }
            ],
        "search_strategy": "semantic",  # 仅语义检索
        "rerank_strategy": "rrf",  # 语义检索时重排序策略不重要
        "rrf_k": 60  # RRF参数
    }
    
    return send_request("仅语义检索", request_data)

def test_hybrid_search_full_text_only():
    """测试仅全文检索模式"""
    print("=" * 60)
    print("📝 测试仅全文检索模式")
    print("=" * 60)
    
    # 准备测试数据
    request_data = {
        "text": "iPhone 15",
        "top_k": 5,
        "total_results": 10,
        "databases": [
            {
                "database": "lkz_test_V1",
                "collections": ["products_hybrid"]  # 正确指定集合
                }
            ],
        "search_strategy": "full_text",  # 仅全文检索
        "rerank_strategy": "rrf",  # 全文检索时重排序策略不重要
        "rrf_k": 80  # RRF参数
    }
    
    return send_request("仅全文检索", request_data)

def test_hybrid_search_combined():
    """测试混合检索模式（语义+全文）使用RRF重排序"""
    print("=" * 60)
    print("🔀 测试混合检索模式（语义+全文）使用RRF重排序")
    print("=" * 60)
    
    # 准备测试数据
    request_data = {
        "text": "苹果手机电池续航怎么样",
        "top_k": 5,
        "total_results": 10,
        "databases": [
            {
                "database": "lkz_test_V1",
                "collections": ["products_hybrid"]  # 正确指定集合
                }
            ],
        "search_strategy": "hybrid",  # 混合检索
        "rerank_strategy": "rrf",  # 使用RRF重排序
        "rrf_k": 60  # RRF平滑参数k
    }
    
    return send_request("混合检索(RRF)", request_data)

def test_hybrid_search_weighted_reranking():
    """测试加权重排序策略"""
    print("=" * 60)
    print("⚖️ 测试加权重排序策略")
    print("=" * 60)
    
    # 准备测试数据
    request_data = {
        "text": "电脑笔记本性能游戏",
        "top_k": 5,
        "total_results": 10,
        "databases": [
            {
                "database": "lkz_test_V1",
                "collections": ["documents", "products"]  # 使用更通用的集合名称
            }
        ],
        "search_strategy": "hybrid",
        "rerank_strategy": "weighted",  # 使用加权重排序
        "dense_weight": 0.7,  # 语义检索权重（更重视语义）
        "sparse_weight": 0.3   # 全文检索权重
    }
    
    return send_request("混合检索(加权)", request_data)

def test_multi_database_hybrid_search():
    """测试多数据库混合检索（使用RRF）"""
    print("=" * 60)
    print("🗄️ 测试多数据库混合检索（使用RRF）")
    print("=" * 60)
    
    # 准备测试数据
    request_data = {
        "text": "产品质量问题售后服务",
        "top_k": 3,
        "total_results": 15,
        "databases": [
            {
                "database": "lkz_test_V1",
                "collections": ["products_hybrid"]  # 正确指定集合
                }
            ],
        "search_strategy": "hybrid",
        "rerank_strategy": "rrf",
        "rrf_k": 50,  # RRF平滑参数
        "metadata_filters": {
            "category": "electronics",
            "rating": {"$gte": 4.0}
        }
    }
    
    return send_request("多数据库混合检索", request_data)

def test_hybrid_search_with_filters():
    """测试带筛选条件的混合检索（使用加权重排序）"""
    print("=" * 60)
    print("🔧 测试带筛选条件的混合检索（使用加权重排序）")
    print("=" * 60)
    
    # 准备测试数据
    request_data = {
        "text": "高端智能手机推荐",
        "top_k": 5,
        "total_results": 10,
        "databases": [
            {
                "database": "lkz_test_V1",
                "collections": ["products_hybrid"]  # 正确指定集合
                }
            ],
        "search_strategy": "hybrid",
        "rerank_strategy": "weighted",  # 使用加权重排序
        "dense_weight": 0.6,  # 语义检索权重
        "sparse_weight": 0.4,  # 全文检索权重
        "filter_expr": "price >= 5000 and in_stock == true",
        "metadata_filters": {
            "brand": "Apple",
            "category": "smartphones"
        }
    }
    
    return send_request("带筛选条件的混合检索", request_data)

def send_request(test_name: str, request_data: Dict[str, Any]) -> bool:
    """发送请求并处理响应"""
    print(f"测试用例: {test_name}")
    print(f"请求体: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    try:
        headers = {"Content-Type": "application/json"}
        
        # 转换为JSON字符串
        payload_str = json.dumps(request_data, ensure_ascii=False)
            
        print(f"发送请求到: {SEARCH_ENDPOINT}")
        
        # 记录开始时间
        start_time = time.time()
        
        # 发送请求
        response = requests.post(SEARCH_ENDPOINT, data=payload_str.encode('utf-8'), headers=headers)
        
        # 计算耗时
        elapsed_time = time.time() - start_time
        
        print(f"状态码: {response.status_code}")
        print(f"请求耗时: {elapsed_time:.3f}秒")
        
        if response.status_code == 200:
            # 解析JSON响应
            try:
                result = response.json()
                
                # 从嵌套的data字段中获取数据
                data = result.get('data', {})
                
                print(f"查询文本: {data.get('query')}")
                print(f"搜索耗时: {result.get('search_time')}")
                print(f"搜索的数据库数量: {result.get('databases_searched')}")
                print(f"总结果数: {data.get('total_results')}")
                
                # 打印混合检索特有信息
                print(f"检索策略: {result.get('search_strategy')}")
                print(f"重排序策略: {result.get('rerank_strategy')}")
                
                # 检查是否有错误信息
                if 'errors' in result:
                    print(f"错误数量: {result.get('error_count', 0)}")
                    print(f"错误详情: {result.get('errors', [])}")
                
                # 打印前3条结果
                results = data.get('results', [])
                print(f"\n找到 {len(results)} 条结果:")
                
                # 按搜索类型分组显示
                search_types = {}
                for r in results:
                    search_type = r.get('search_type', 'unknown')
                    if search_type not in search_types:
                        search_types[search_type] = []
                    search_types[search_type].append(r)
                
                for search_type, type_results in search_types.items():
                    print(f"\n  📊 {search_type} 类型结果 ({len(type_results)}条):")
                    for i, r in enumerate(type_results[:2]):
                        print(f"    结果 {i+1}:")
                        print(f"      内容: {r.get('content', '')[:100]}...")
                        print(f"      相似度: {r.get('similarity', 0):.4f}")
                        print(f"      数据库: {r.get('database', '')}")
                        print(f"      集合: {r.get('collection', '')}")
                        
                        # 显示混合检索特有字段
                        if 'search_strategy' in r:
                            print(f"      搜索策略: {r['search_strategy']}")
                        if 'rerank_strategy' in r:
                            print(f"      重排序策略: {r['rerank_strategy']}")
                        if 'note' in r:
                            print(f"      备注: {r['note']}")
                    
                    if len(type_results) > 2:
                        print(f"      ... 还有 {len(type_results) - 2} 条结果")
                
                print("✅ 测试成功!")
                return True
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式!")
                print(f"响应体: {response.text}")
                return False
        else:
            print("❌ 测试失败!")
            print(f"响应体: {response.text}")
            
            # 尝试解析错误响应中的JSON信息
            try:
                error_result = response.json()
                if 'detail' in error_result:
                    print(f"错误详情: {error_result['detail']}")
                if 'errors' in error_result:
                    print(f"具体错误: {error_result['errors']}")
            except:
                print("无法解析错误响应为JSON格式")
            
            return False
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始混合检索多数据库搜索测试")
    print("=" * 80)
    
    print("📋 测试说明：")
    print("- 本测试需要先准备支持混合检索的数据库和集合")
    print("- 测试的数据库：documents_hybrid, knowledge_base")
    print("- 测试的集合：documents, products, faq")
    print("- 如果数据库/集合不存在，对应测试会报错，这是正常现象")
    print("- 建议先运行 upload_texts_batch_hybrid 接口创建测试数据")
    print("=" * 80)
    
    # 测试用例列表
    test_cases = [
        # test_hybrid_search_semantic_only,  # 启用语义检索测试
        # test_hybrid_search_full_text_only,
        test_hybrid_search_combined,
        # test_hybrid_search_weighted_reranking,
        # test_multi_database_hybrid_search,
        # test_hybrid_search_with_filters
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for test_func in test_cases:
        try:
            success = test_func()
            if success:
                success_count += 1
            print("\n" + "="*80 + "\n")
        except Exception as e:
            print(f"❌ 测试函数 {test_func.__name__} 执行失败: {e}")
            print("\n" + "="*80 + "\n")
    
    # 打印测试总结
    print("🏁 测试总结")
    print("=" * 60)
    print(f"总测试数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  部分测试失败，请检查服务状态和数据配置")

if __name__ == "__main__":
    main() 