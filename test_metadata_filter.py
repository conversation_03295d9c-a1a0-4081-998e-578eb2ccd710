#!/usr/bin/env python3
"""
测试metadata筛选功能的脚本
"""

import asyncio
import json
import requests
import time

# API基础URL
BASE_URL = "http://localhost:8000/api/v1"

def test_upload_sample_data():
    """上传测试数据"""
    print("=== 上传测试数据 ===")
    
    # 准备测试数据
    test_data = {
        "texts": [
            "苹果iPhone 14 Pro 智能手机，配备A16芯片",
            "三星Galaxy S23 Ultra 旗舰手机，拍照功能强大", 
            "华为MateBook X Pro 轻薄笔记本电脑",
            "戴尔XPS 13 商务笔记本，性能卓越",
            "索尼WH-1000XM4 降噪耳机，音质出色"
        ],
        "collection": "test_products",
        "database": "test_db",
        "metadata": {
            "source": "product_catalog",
            "upload_time": "2024-01-15"
        }
    }
    
    # 为每个文本添加不同的产品信息
    products_info = [
        {
            "product_info": {
                "category": "electronics",
                "subcategory": "smartphone",
                "brand": "Apple"
            },
            "price": 7999,
            "in_stock": True,
            "rating": 4.8,
            "tags": ["flagship", "new"]
        },
        {
            "product_info": {
                "category": "electronics", 
                "subcategory": "smartphone",
                "brand": "Samsung"
            },
            "price": 8999,
            "in_stock": True,
            "rating": 4.7,
            "tags": ["flagship", "camera"]
        },
        {
            "product_info": {
                "category": "electronics",
                "subcategory": "laptop", 
                "brand": "Huawei"
            },
            "price": 12999,
            "in_stock": False,
            "rating": 4.6,
            "tags": ["business", "lightweight"]
        },
        {
            "product_info": {
                "category": "electronics",
                "subcategory": "laptop",
                "brand": "Dell"
            },
            "price": 9999,
            "in_stock": True,
            "rating": 4.5,
            "tags": ["business", "performance"]
        },
        {
            "product_info": {
                "category": "electronics",
                "subcategory": "headphones",
                "brand": "Sony"
            },
            "price": 2299,
            "in_stock": True,
            "rating": 4.9,
            "tags": ["audio", "noise_cancelling"]
        }
    ]
    
    # 逐个上传每个产品
    for i, (text, product_info) in enumerate(zip(test_data["texts"], products_info)):
        upload_data = {
            "texts": [text],
            "collection": test_data["collection"],
            "database": test_data["database"],
            "metadata": {**test_data["metadata"], **product_info}
        }
        
        try:
            response = requests.post(f"{BASE_URL}/upload_texts", json=upload_data)
            if response.status_code == 200:
                print(f"✅ 成功上传产品 {i+1}: {text[:30]}...")
            else:
                print(f"❌ 上传产品 {i+1} 失败: {response.text}")
        except Exception as e:
            print(f"❌ 上传产品 {i+1} 时出错: {e}")
    
    print("测试数据上传完成\n")

def test_metadata_filter_search():
    """测试metadata筛选搜索"""
    print("=== 测试metadata筛选搜索 ===")
    
    # 测试用例
    test_cases = [
        {
            "name": "搜索智能手机",
            "request": {
                "text": "手机",
                "top_k": 5,
                "collection": "test_products",
                "database": "test_db",
                "metadata_filters": {
                    "product_info": {
                        "subcategory": "smartphone"
                    }
                }
            }
        },
        {
            "name": "搜索价格低于10000的产品",
            "request": {
                "text": "电子产品",
                "top_k": 5,
                "collection": "test_products", 
                "database": "test_db",
                "metadata_filters": {
                    "price": {"$lt": 10000}
                }
            }
        },
        {
            "name": "搜索有库存的苹果产品",
            "request": {
                "text": "苹果",
                "top_k": 5,
                "collection": "test_products",
                "database": "test_db",
                "metadata_filters": {
                    "in_stock": True,
                    "product_info": {
                        "brand": "Apple"
                    }
                }
            }
        },
        {
            "name": "使用filter_expr搜索笔记本",
            "request": {
                "text": "笔记本",
                "top_k": 5,
                "collection": "test_products",
                "database": "test_db",
                "filter_expr": "metadata[\"product_info\"][\"subcategory\"] == \"laptop\""
            }
        },
        {
            "name": "组合筛选：评分高且有库存",
            "request": {
                "text": "电子产品",
                "top_k": 5,
                "collection": "test_products",
                "database": "test_db",
                "metadata_filters": {
                    "rating": {"$gte": 4.7},
                    "in_stock": True
                }
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        try:
            start_time = time.time()
            response = requests.post(f"{BASE_URL}/search", json=test_case["request"])
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 搜索成功，耗时: {end_time - start_time:.3f}秒")
                print(f"找到 {result['total_results']} 条结果:")
                
                for i, item in enumerate(result["results"]):
                    print(f"  {i+1}. 内容: {item['content'][:50]}...")
                    print(f"     相似度: {item['similarity']:.3f}")
                    if 'metadata' in item:
                        metadata = item['metadata']
                        if isinstance(metadata, dict):
                            brand = metadata.get('product_info', {}).get('brand', 'N/A')
                            price = metadata.get('price', 'N/A')
                            in_stock = metadata.get('in_stock', 'N/A')
                            print(f"     品牌: {brand}, 价格: {price}, 库存: {in_stock}")
            else:
                print(f"❌ 搜索失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 搜索时出错: {e}")

def main():
    """主函数"""
    print("开始测试metadata筛选功能...\n")
    
    # 上传测试数据
    test_upload_sample_data()
    
    # 等待数据处理完成
    print("等待3秒让数据处理完成...")
    time.sleep(3)
    
    # 测试搜索功能
    test_metadata_filter_search()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
