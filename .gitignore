# 忽略 Python 编译文件
*.pyc

# 忽略日志文件和目录
logs/
*.log

# 忽略环境配置文件
.env
.env.local
.env.development
.env.test
.env.production

# 忽略 Python 相关文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 忽略虚拟环境
venv/
env/
ENV/

# 忽略IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 忽略临时文件
.DS_Store
Thumbs.db

# 忽略测试覆盖率文件
.coverage
htmlcov/
.pytest_cache/
