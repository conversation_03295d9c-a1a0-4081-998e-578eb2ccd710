"""
FastAPI应用入口文件
"""
import sys
import logging

# 首先设置日志配置
from app.config.logging_config import setup_logging
setup_logging(level="INFO")

logger = logging.getLogger(__name__)

# 加载环境配置
from app.config.environment import load_and_validate_environment
load_and_validate_environment()

# 尝试导入必要的库
try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    from app.routes import router
    from app.core.lifecycle import lifespan
    from app.middleware.exception_handler import setup_exception_handlers
except ImportError as e:
    logger.error(f"无法导入必要的库: {e}")
    logger.error("请安装缺失的库，或检查是否有DLL加载问题。")
    logger.error("建议安装以下库：")
    logger.error("pip install fastapi uvicorn pymupdf python-docx pdfplumber PyPDF2")
    sys.exit(1)


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    # 创建FastAPI应用
    app = FastAPI(
        title="RAG Vector Service",
        description="RAG向量服务API",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 设置异常处理器
    setup_exception_handlers(app)
    
    # 集成路由
    app.include_router(router)
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        return {"status": "ok", "service": "RAG Vector Service"}
    
    return app


# 创建应用实例
app = create_app()

if __name__ == "__main__":
    import uvicorn
    
    logger.info("启动RAG服务...")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        workers=1,  # 开发环境使用单worker
        timeout_keep_alive=300,
        log_config=None  # 使用我们自己的日志配置
    )