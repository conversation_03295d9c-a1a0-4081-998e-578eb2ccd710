"""
数据处理工具模块
提供数据转换和处理功能
"""
import json
from typing import Any, Dict, List, Optional, Union, Callable
from uuid import uuid4
from pydantic import BaseModel


class DataConverter:
    """数据转换器"""
    
    @staticmethod
    def ensure_string(value: Union[str, int, float]) -> str:
        """
        确保值为字符串类型
        
        Args:
            value: 输入值
            
        Returns:
            str: 字符串值
        """
        if isinstance(value, str):
            return value.strip()
        return str(value)
    
    @staticmethod
    def ensure_int(value: Union[str, int, float]) -> int:
        """
        确保值为整数类型
        
        Args:
            value: 输入值
            
        Returns:
            int: 整数值
            
        Raises:
            ValueError: 转换失败
        """
        if isinstance(value, int):
            return value
        if isinstance(value, str):
            # 移除可能的特殊字符
            clean_value = value.strip().split('?')[0]
            return int(clean_value)
        if isinstance(value, float):
            return int(value)
        raise ValueError(f"无法将 {value} 转换为整数")
    
    @staticmethod
    def ensure_float(value: Union[str, int, float]) -> float:
        """
        确保值为浮点数类型
        
        Args:
            value: 输入值
            
        Returns:
            float: 浮点数值
            
        Raises:
            ValueError: 转换失败
        """
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            clean_value = value.strip()
            return float(clean_value)
        raise ValueError(f"无法将 {value} 转换为浮点数")
    
    @staticmethod
    def ensure_bool(value: Union[str, bool, int]) -> bool:
        """
        确保值为布尔类型
        
        Args:
            value: 输入值
            
        Returns:
            bool: 布尔值
        """
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        if isinstance(value, int):
            return bool(value)
        return False
    
    @staticmethod
    def safe_json_loads(json_str: str, default: Any = None) -> Any:
        """
        安全的JSON解析
        
        Args:
            json_str: JSON字符串
            default: 解析失败时的默认值
            
        Returns:
            Any: 解析结果或默认值
        """
        if not json_str:
            return default
        
        try:
            return json.loads(json_str)
        except (json.JSONDecodeError, TypeError):
            return default
    
    @staticmethod
    def safe_json_dumps(obj: Any, default: str = "{}") -> str:
        """
        安全的JSON序列化
        
        Args:
            obj: 要序列化的对象
            default: 序列化失败时的默认值
            
        Returns:
            str: JSON字符串或默认值
        """
        try:
            return json.dumps(obj, ensure_ascii=False)
        except (TypeError, ValueError):
            return default


class ListProcessor:
    """列表处理器"""
    
    @staticmethod
    def chunk_list(items: List[Any], chunk_size: int) -> List[List[Any]]:
        """
        将列表分块
        
        Args:
            items: 输入列表
            chunk_size: 块大小
            
        Returns:
            List[List[Any]]: 分块后的列表
        """
        if chunk_size <= 0:
            return [items] if items else []
        
        chunks = []
        for i in range(0, len(items), chunk_size):
            chunks.append(items[i:i + chunk_size])
        return chunks
    
    @staticmethod
    def flatten_list(nested_list: List[List[Any]]) -> List[Any]:
        """
        扁平化嵌套列表
        
        Args:
            nested_list: 嵌套列表
            
        Returns:
            List[Any]: 扁平化后的列表
        """
        flattened = []
        for sublist in nested_list:
            if isinstance(sublist, list):
                flattened.extend(sublist)
            else:
                flattened.append(sublist)
        return flattened
    
    @staticmethod
    def remove_duplicates(items: List[Any], key_func: Optional[Callable] = None) -> List[Any]:
        """
        移除列表中的重复项
        
        Args:
            items: 输入列表
            key_func: 用于确定唯一性的键函数
            
        Returns:
            List[Any]: 去重后的列表
        """
        if not items:
            return []
        
        if key_func is None:
            # 使用集合去重，保持顺序
            seen = set()
            result = []
            for item in items:
                if item not in seen:
                    seen.add(item)
                    result.append(item)
            return result
        else:
            # 使用键函数去重
            seen = set()
            result = []
            for item in items:
                key = key_func(item)
                if key not in seen:
                    seen.add(key)
                    result.append(item)
            return result


class TextProcessor:
    """文本处理器"""
    
    @staticmethod
    def chunk_text(text: str, chunk_size: int, chunk_overlap: int = 0) -> List[str]:
        """
        将文本分块
        
        Args:
            text: 输入文本
            chunk_size: 块大小
            chunk_overlap: 块重叠大小
            
        Returns:
            List[str]: 分块后的文本列表
        """
        if not text:
            return []
        
        if chunk_size <= 0:
            return [text]
        
        if chunk_overlap >= chunk_size:
            chunk_overlap = chunk_size // 2
        
        chunks = []
        start = 0
        step = chunk_size - chunk_overlap
        
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            if chunk.strip():  # 只添加非空块
                chunks.append(chunk)
            
            if end >= len(text):
                break
            
            start += step
        
        return chunks
    
    @staticmethod
    def clean_text(text: str) -> str:
        """
        清理文本
        
        Args:
            text: 输入文本
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        cleaned = ' '.join(text.split())
        return cleaned.strip()
    
    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """
        截断文本
        
        Args:
            text: 输入文本
            max_length: 最大长度
            suffix: 后缀
            
        Returns:
            str: 截断后的文本
        """
        if not text or len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix


class MetadataProcessor:
    """元数据处理器"""
    
    @staticmethod
    def merge_metadata(
        base_metadata: Dict[str, Any],
        additional_metadata: Dict[str, Any],
        overwrite: bool = True
    ) -> Dict[str, Any]:
        """
        合并元数据
        
        Args:
            base_metadata: 基础元数据
            additional_metadata: 额外元数据
            overwrite: 是否覆盖已存在的键
            
        Returns:
            Dict[str, Any]: 合并后的元数据
        """
        result = base_metadata.copy()
        
        for key, value in additional_metadata.items():
            if overwrite or key not in result:
                result[key] = value
        
        return result
    
    @staticmethod
    def extract_metadata_from_request(
        request_obj: BaseModel,
        standard_fields: List[str]
    ) -> Dict[str, Any]:
        """
        从请求对象中提取元数据字段
        
        Args:
            request_obj: 请求对象
            standard_fields: 标准字段列表
            
        Returns:
            Dict[str, Any]: 提取的元数据
        """
        metadata = {}
        
        # 获取所有字段
        try:
            all_fields = request_obj.model_dump(exclude_unset=True)
        except AttributeError:
            all_fields = request_obj.dict(exclude_unset=True)
        
        # 提取非标准字段作为元数据
        for field, value in all_fields.items():
            if field not in standard_fields and value is not None:
                metadata[field] = value
        
        return metadata
    
    @staticmethod
    def clean_metadata(metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理元数据，移除空值和无效值
        
        Args:
            metadata: 输入元数据
            
        Returns:
            Dict[str, Any]: 清理后的元数据
        """
        cleaned = {}
        
        for key, value in metadata.items():
            if value is not None and value != "":
                # 清理字符串值
                if isinstance(value, str):
                    value = value.strip()
                    if value:  # 非空字符串
                        cleaned[key] = value
                else:
                    cleaned[key] = value
        
        return cleaned


class RecordBuilder:
    """记录构建器"""
    
    @staticmethod
    def build_vector_record(
        content: str,
        vector: List[float],
        metadata: Dict[str, Any],
        record_id: Optional[Union[str, int]] = None,
        collection: str = "documents",
        encrypted: bool = False
    ) -> Dict[str, Any]:
        """
        构建向量记录
        
        Args:
            content: 文本内容
            vector: 向量
            metadata: 元数据
            record_id: 记录ID
            collection: 集合名称
            encrypted: 是否加密
            
        Returns:
            Dict[str, Any]: 向量记录
        """
        record = {
            "content": content,
            "vector": vector,
            "metadata": {
                "collection": collection,
                "encrypted": encrypted,
                **metadata
            }
        }
        
        if record_id is not None:
            record["id"] = record_id
        
        return record
    
    @staticmethod
    def build_hybrid_vector_record(
        content: str,
        vector: List[float],
        metadata: Dict[str, Any],
        record_id: Optional[Union[str, int]] = None,
        collection: str = "documents_hybrid",
        encrypted: bool = False
    ) -> Dict[str, Any]:
        """
        构建混合向量记录
        
        Args:
            content: 文本内容（用于存储和BM25全文检索，非加密）
            vector: 密集向量
            metadata: 元数据
            record_id: 记录ID
            collection: 集合名称
            encrypted: 是否加密
            
        Returns:
            Dict[str, Any]: 混合向量记录
        """
        record = {
            "content": content,
            "vector": vector,
            "metadata": {
                "collection": collection,
                "encrypted": encrypted,
                **metadata
            }
        }
        
        if record_id is not None:
            record["id"] = record_id
        
        return record
    
    @staticmethod
    def build_batch_records(
        texts: List[str],
        vectors: List[List[float]],
        base_metadata: Dict[str, Any],
        doc_id: str,
        collection: str = "documents",
        encrypted: bool = False,
        ids: Optional[List[Union[str, int]]] = None
    ) -> List[Dict[str, Any]]:
        """
        构建批量记录
        
        Args:
            texts: 文本列表
            vectors: 向量列表
            base_metadata: 基础元数据
            doc_id: 文档ID
            collection: 集合名称
            encrypted: 是否加密
            ids: ID列表
            
        Returns:
            List[Dict[str, Any]]: 批量记录列表
        """
        records = []
        
        for i, (text, vector) in enumerate(zip(texts, vectors)):
            metadata = {
                "doc_id": doc_id,
                "text_index": i,
                **base_metadata
            }
            
            record_id = ids[i] if ids else None
            
            record = RecordBuilder.build_vector_record(
                content=text,
                vector=vector,
                metadata=metadata,
                record_id=record_id,
                collection=collection,
                encrypted=encrypted
            )
            
            records.append(record)
        
        return records


class IDGenerator:
    """ID生成器"""
    
    @staticmethod
    def generate_uuid() -> str:
        """生成UUID"""
        return str(uuid4())
    
    @staticmethod
    def generate_doc_id(prefix: str = "doc") -> str:
        """生成文档ID"""
        return f"{prefix}_{uuid4()}"
    
    @staticmethod
    def generate_batch_id(prefix: str = "batch") -> str:
        """生成批次ID"""
        return f"{prefix}_{uuid4()}"
    
    @staticmethod
    def validate_id_format(id_value: Union[str, int]) -> bool:
        """
        验证ID格式
        
        Args:
            id_value: ID值
            
        Returns:
            bool: 是否有效
        """
        if isinstance(id_value, int):
            return id_value >= 0
        if isinstance(id_value, str):
            return len(id_value.strip()) > 0
        return False


class FieldExtractor:
    """字段提取器"""
    
    @staticmethod
    def extract_text_content(item: Dict[str, Any]) -> str:
        """
        从字典中提取文本内容
        
        Args:
            item: 输入字典
            
        Returns:
            str: 文本内容
        """
        # 按优先级查找文本字段
        text_fields = ['text', 'content', 'message', 'description', 'body']
        
        for field in text_fields:
            if field in item and item[field]:
                return str(item[field]).strip()
        
        return ""
    
    @staticmethod
    def extract_id_value(item: Dict[str, Any]) -> Optional[Union[str, int]]:
        """
        从字典中提取ID值
        
        Args:
            item: 输入字典
            
        Returns:
            Optional[Union[str, int]]: ID值
        """
        # 按优先级查找ID字段
        id_fields = ['id', '_id', 'uuid', 'record_id', 'doc_id']
        
        for field in id_fields:
            if field in item and item[field] is not None:
                return item[field]
        
        return None


# 便捷函数
def convert_to_int_safe(value: Union[str, int, float], default: int = 0) -> int:
    """安全转换为整数"""
    try:
        return DataConverter.ensure_int(value)
    except (ValueError, TypeError):
        return default


def convert_to_float_safe(value: Union[str, int, float], default: float = 0.0) -> float:
    """安全转换为浮点数"""
    try:
        return DataConverter.ensure_float(value)
    except (ValueError, TypeError):
        return default


def merge_dicts_safe(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """安全合并字典"""
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result


def clean_empty_values(data: Dict[str, Any]) -> Dict[str, Any]:
    """清理空值"""
    return {k: v for k, v in data.items() if v is not None and v != ""}


def build_search_summary(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """构建搜索结果摘要"""
    if not results:
        return {"total": 0, "collections": [], "databases": []}
    
    collections = set()
    databases = set()
    
    for result in results:
        if 'collection' in result:
            collections.add(result['collection'])
        if 'database' in result:
            databases.add(result['database'])
    
    return {
        "total": len(results),
        "collections": list(collections),
        "databases": list(databases)
    }